<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.quartz.mapper.SystemRequestRecordJobMapper">

    <resultMap type="com.haoys.user.quartz.domain.SystemRequestRecordJob" id="SystemRequestRecordJobResult">
        <result property="jobId" column="job_id"/>
        <result property="jobName" column="job_name"/>
        <result property="jobGroup" column="job_group"/>
        <result property="invokeTarget" column="invoke_target"/>
        <result property="cronExpression" column="cron_expression"/>
        <result property="misfirePolicy" column="misfire_policy"/>
        <result property="concurrent" column="concurrent"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="description" column="description"/>
        <result property="jobType" column="job_type"/>
        <result property="jobParams" column="job_params"/>
        <result property="nextValidTime" column="next_valid_time"/>
        <result property="previousFireTime" column="previous_fire_time"/>
        <result property="executeStatus" column="execute_status"/>
        <result property="lastExecuteResult" column="last_execute_result"/>
        <result property="executeCount" column="execute_count"/>
        <result property="failureCount" column="failure_count"/>
        <result property="avgExecuteTime" column="avg_execute_time"/>
        <result property="maxExecuteTime" column="max_execute_time"/>
        <result property="minExecuteTime" column="min_execute_time"/>
        <result property="enabled" column="enabled"/>
        <result property="priority" column="priority"/>
        <result property="timeoutSeconds" column="timeout_seconds"/>
        <result property="retryCount" column="retry_count"/>
        <result property="retryInterval" column="retry_interval"/>
        <result property="category" column="category"/>
        <result property="tags" column="tags"/>
        <result property="dependsOn" column="depends_on"/>
        <result property="executeNode" column="execute_node"/>
        <result property="sendNotification" column="send_notification"/>
        <result property="notificationEmail" column="notification_email"/>
        <result property="notificationPhone" column="notification_phone"/>
        <result property="configJson" column="config_json"/>
        <result property="ext1" column="ext1"/>
        <result property="ext2" column="ext2"/>
        <result property="ext3" column="ext3"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="version" column="version"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectJobVo">
        select job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, 
               create_by, create_time, update_by, update_time, remark, description, job_type, job_params,
               next_valid_time, previous_fire_time, execute_status, last_execute_result, execute_count, failure_count,
               avg_execute_time, max_execute_time, min_execute_time, enabled, priority, timeout_seconds, retry_count,
               retry_interval, category, tags, depends_on, execute_node, send_notification, notification_email,
               notification_phone, config_json, ext1, ext2, ext3, deleted, delete_time, delete_by, version, tenant_id
        from system_request_record_job
    </sql>

    <select id="selectJobList" parameterType="com.haoys.user.quartz.domain.SystemRequestRecordJob" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        <where>
            <if test="jobName != null and jobName != ''">
                AND job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                AND job_group = #{jobGroup}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="jobType != null">
                AND job_type = #{jobType}
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="enabled != null">
                AND enabled = #{enabled}
            </if>
            <if test="executeNode != null and executeNode != ''">
                AND execute_node = #{executeNode}
            </if>
            AND deleted = 0
        </where>
        order by job_id desc
    </select>

    <select id="selectJobAll" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where deleted = 0
        order by job_id desc
    </select>

    <select id="selectJobById" parameterType="Long" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where job_id = #{jobId} and deleted = 0
    </select>

    <select id="selectJobByNameAndGroup" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where job_name = #{jobName} and job_group = #{jobGroup} and deleted = 0
    </select>

    <select id="selectEnabledJobList" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where status = '0' and enabled = 1 and deleted = 0
        order by priority desc, job_id asc
    </select>

    <select id="selectJobListByType" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where job_type = #{jobType} and deleted = 0
        order by job_id desc
    </select>

    <select id="selectJobListByCategory" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where category = #{category} and deleted = 0
        order by job_id desc
    </select>

    <select id="selectUpcomingJobs" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where status = '0' and enabled = 1 and deleted = 0
        and next_valid_time between now() and date_add(now(), interval #{minutes} minute)
        order by next_valid_time asc
    </select>

    <select id="selectLongTimeNoExecuteJobs" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where status = '0' and enabled = 1 and deleted = 0
        and (previous_fire_time is null or previous_fire_time &lt; date_sub(now(), interval #{hours} hour))
        order by previous_fire_time asc
    </select>

    <select id="selectFailedJobs" resultMap="SystemRequestRecordJobResult">
        <include refid="selectJobVo"/>
        where execute_status = '1' and deleted = 0
        order by update_time desc
    </select>

    <select id="selectJobStatistics" resultType="map">
        select 
            count(*) as totalJobs,
            sum(case when status = '0' and enabled = 1 then 1 else 0 end) as runningJobs,
            sum(case when status = '1' or enabled = 0 then 1 else 0 end) as pausedJobs,
            sum(case when execute_status = '1' then 1 else 0 end) as errorJobs,
            avg(case when avg_execute_time > 0 then avg_execute_time else null end) as avgExecuteTime,
            sum(execute_count) as totalExecutions,
            sum(failure_count) as totalFailures,
            case 
                when sum(execute_count) > 0 then round((sum(execute_count) - sum(failure_count)) * 100.0 / sum(execute_count), 2)
                else 0 
            end as successRate
        from system_request_record_job 
        where deleted = 0
    </select>

    <select id="selectJobExecuteTrend" resultType="map">
        select 
            date_format(l.start_time, '%Y-%m-%d') as executeDate,
            count(*) as executeCount,
            sum(case when l.status = '0' then 1 else 0 end) as successCount,
            sum(case when l.status = '1' then 1 else 0 end) as failureCount,
            avg(l.execute_time) as avgExecuteTime
        from system_request_record_job_log l
        where l.start_time >= date_sub(now(), interval #{days} day)
        group by date_format(l.start_time, '%Y-%m-%d')
        order by executeDate desc
    </select>

    <select id="selectJobPerformanceRanking" resultType="map">
        select 
            j.job_id as jobId,
            j.job_name as jobName,
            j.job_group as jobGroup,
            j.execute_count as executeCount,
            j.avg_execute_time as avgExecuteTime,
            j.max_execute_time as maxExecuteTime,
            j.failure_count as failureCount,
            case 
                when j.execute_count > 0 then round((j.execute_count - j.failure_count) * 100.0 / j.execute_count, 2)
                else 0 
            end as successRate
        from system_request_record_job j
        where j.deleted = 0 and j.execute_count > 0
        order by j.avg_execute_time desc
        limit #{limit}
    </select>

    <select id="checkJobNameUnique" resultType="int">
        select count(1) from system_request_record_job 
        where job_name = #{jobName} and job_group = #{jobGroup} and deleted = 0
        <if test="jobId != null">
            and job_id != #{jobId}
        </if>
    </select>

    <insert id="insertJob" parameterType="com.haoys.user.quartz.domain.SystemRequestRecordJob" useGeneratedKeys="true" keyProperty="jobId">
        insert into system_request_record_job(
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="jobName != null and jobName != ''">job_name,</if>
                <if test="jobGroup != null and jobGroup != ''">job_group,</if>
                <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
                <if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
                <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
                <if test="concurrent != null and concurrent != ''">concurrent,</if>
                <if test="status != null and status != ''">status,</if>
                <if test="createBy != null and createBy != ''">create_by,</if>
                <if test="remark != null and remark != ''">remark,</if>
                <if test="description != null and description != ''">description,</if>
                <if test="jobType != null">job_type,</if>
                <if test="jobParams != null">job_params,</if>
                <if test="enabled != null">enabled,</if>
                <if test="priority != null">priority,</if>
                <if test="timeoutSeconds != null">timeout_seconds,</if>
                <if test="retryCount != null">retry_count,</if>
                <if test="retryInterval != null">retry_interval,</if>
                <if test="category != null and category != ''">category,</if>
                <if test="tags != null and tags != ''">tags,</if>
                <if test="dependsOn != null and dependsOn != ''">depends_on,</if>
                <if test="executeNode != null and executeNode != ''">execute_node,</if>
                <if test="sendNotification != null">send_notification,</if>
                <if test="notificationEmail != null and notificationEmail != ''">notification_email,</if>
                <if test="notificationPhone != null and notificationPhone != ''">notification_phone,</if>
                <if test="configJson != null">config_json,</if>
                <if test="ext1 != null and ext1 != ''">ext1,</if>
                <if test="ext2 != null and ext2 != ''">ext2,</if>
                <if test="ext3 != null and ext3 != ''">ext3,</if>
                <if test="tenantId != null and tenantId != ''">tenant_id,</if>
                create_time
            </trim>
        )values(
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="jobName != null and jobName != ''">#{jobName},</if>
                <if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
                <if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
                <if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
                <if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
                <if test="concurrent != null and concurrent != ''">#{concurrent},</if>
                <if test="status != null and status != ''">#{status},</if>
                <if test="createBy != null and createBy != ''">#{createBy},</if>
                <if test="remark != null and remark != ''">#{remark},</if>
                <if test="description != null and description != ''">#{description},</if>
                <if test="jobType != null">#{jobType},</if>
                <if test="jobParams != null">#{jobParams},</if>
                <if test="enabled != null">#{enabled},</if>
                <if test="priority != null">#{priority},</if>
                <if test="timeoutSeconds != null">#{timeoutSeconds},</if>
                <if test="retryCount != null">#{retryCount},</if>
                <if test="retryInterval != null">#{retryInterval},</if>
                <if test="category != null and category != ''">#{category},</if>
                <if test="tags != null and tags != ''">#{tags},</if>
                <if test="dependsOn != null and dependsOn != ''">#{dependsOn},</if>
                <if test="executeNode != null and executeNode != ''">#{executeNode},</if>
                <if test="sendNotification != null">#{sendNotification},</if>
                <if test="notificationEmail != null and notificationEmail != ''">#{notificationEmail},</if>
                <if test="notificationPhone != null and notificationPhone != ''">#{notificationPhone},</if>
                <if test="configJson != null">#{configJson},</if>
                <if test="ext1 != null and ext1 != ''">#{ext1},</if>
                <if test="ext2 != null and ext2 != ''">#{ext2},</if>
                <if test="ext3 != null and ext3 != ''">#{ext3},</if>
                <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
                now()
            </trim>
        )
    </insert>

    <update id="updateJob" parameterType="com.haoys.user.quartz.domain.SystemRequestRecordJob">
        update system_request_record_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
            <if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
            <if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
            <if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
            <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
            <if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="description != null">description = #{description},</if>
            <if test="jobType != null">job_type = #{jobType},</if>
            <if test="jobParams != null">job_params = #{jobParams},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="timeoutSeconds != null">timeout_seconds = #{timeoutSeconds},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="retryInterval != null">retry_interval = #{retryInterval},</if>
            <if test="category != null">category = #{category},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="dependsOn != null">depends_on = #{dependsOn},</if>
            <if test="executeNode != null">execute_node = #{executeNode},</if>
            <if test="sendNotification != null">send_notification = #{sendNotification},</if>
            <if test="notificationEmail != null">notification_email = #{notificationEmail},</if>
            <if test="notificationPhone != null">notification_phone = #{notificationPhone},</if>
            <if test="configJson != null">config_json = #{configJson},</if>
            <if test="ext1 != null">ext1 = #{ext1},</if>
            <if test="ext2 != null">ext2 = #{ext2},</if>
            <if test="ext3 != null">ext3 = #{ext3},</if>
            update_time = now()
        </trim>
        where job_id = #{jobId}
    </update>

    <update id="updateJobStatus">
        update system_request_record_job set status = #{status}, update_time = now() where job_id = #{jobId}
    </update>

    <update id="updateJobExecuteStatus">
        update system_request_record_job 
        set execute_status = #{executeStatus}, last_execute_result = #{lastExecuteResult}, update_time = now()
        where job_id = #{jobId}
    </update>

    <update id="updateJobStatistics">
        update system_request_record_job 
        set execute_count = #{executeCount}, failure_count = #{failureCount}, 
            avg_execute_time = #{avgExecuteTime}, max_execute_time = #{maxExecuteTime}, 
            min_execute_time = #{minExecuteTime}, update_time = now()
        where job_id = #{jobId}
    </update>

    <update id="updateJobNextValidTime">
        update system_request_record_job set next_valid_time = #{nextValidTime}, update_time = now() where job_id = #{jobId}
    </update>

    <update id="updateJobPreviousFireTime">
        update system_request_record_job set previous_fire_time = #{previousFireTime}, update_time = now() where job_id = #{jobId}
    </update>

    <update id="batchUpdateJobStatus">
        update system_request_record_job set status = #{status}, update_time = now()
        where job_id in
        <foreach item="jobId" collection="jobIds" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </update>

    <update id="resetJobStatistics">
        update system_request_record_job 
        set execute_count = 0, failure_count = 0, avg_execute_time = 0, 
            max_execute_time = 0, min_execute_time = 0, update_time = now()
        where job_id = #{jobId}
    </update>

    <delete id="deleteJobByIds">
        update system_request_record_job set deleted = 1, delete_time = now()
        where job_id in
        <foreach item="jobId" collection="jobIds" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>

    <delete id="deleteJobById" parameterType="Long">
        update system_request_record_job set deleted = 1, delete_time = now() where job_id = #{jobId}
    </delete>

    <delete id="cleanExpiredJobs">
        delete from system_request_record_job 
        where deleted = 1 and delete_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>
