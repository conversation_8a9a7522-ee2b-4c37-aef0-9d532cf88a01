package com.haoys.user.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.service.SecureTokenService;
import com.haoys.user.service.SystemRequestRecordService;
import com.haoys.user.model.SystemRequestRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统访问日志管理控制器
 * 提供访问日志的查询、统计和监控功能
 * 使用双重验证机制：配置秘钥 + AccessToken
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Api(tags = "系统访问日志管理")
@RestController
@RequestMapping("/access-log")
@Slf4j
public class AccessLogManagementController {

    @Autowired
    private SecureTokenService secureTokenService;

    @Value("${request.access.log.management.secret-key:EDC-ACCESS-LOG-MANAGEMENT-SECRET-2025}")
    private String accessLogSecretKey;

    /**
     * 访问日志管理页面
     */
    @ApiOperation("访问日志管理页面")
    @GetMapping("/management")
    public String accessLogManagementPage(HttpServletRequest request) {
        try {
            // 尝试从classpath读取HTML文件
            ClassPathResource resource = new ClassPathResource("static/access-log-management/management.html");
            if (resource.exists()) {
                String content = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
                
                // 获取服务器信息并注入到页面
                Map<String, Object> serverInfo = getServerInfo(request);
                if (serverInfo != null) {
                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到页面
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/static/access-log-management/management.html");
            log.info("尝试从文件系统读取HTML文件: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));
            
            if (Files.exists(htmlPath)) {
                String content = new String(Files.readAllBytes(htmlPath), StandardCharsets.UTF_8);
                
                // 获取服务器信息并注入到页面
                Map<String, Object> serverInfo = getServerInfo(request);
                if (serverInfo != null) {
                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到页面
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    return content;
                }
            }

            // 如果文件不存在，返回错误页面
            return generateErrorPage("访问日志管理页面文件不存在");

        } catch (IOException e) {
            log.error("读取访问日志管理页面失败", e);
            return generateErrorPage("读取页面文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("访问日志管理页面处理失败", e);
            return generateErrorPage("页面处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证配置秘钥
     */
    @ApiOperation("验证配置秘钥")
    @PostMapping("/auth/verify-secret")
    public CommonResult<Object> verifySecretKey(@RequestBody Map<String, String> request) {
        try {
            String secretKey = request.get("secretKey");
            
            if (secretKey == null || secretKey.trim().isEmpty()) {
                return CommonResult.failed("秘钥不能为空");
            }
            
            if (!accessLogSecretKey.equals(secretKey.trim())) {
                log.warn("访问日志管理秘钥验证失败，输入的秘钥: {}", secretKey);
                return CommonResult.failed("秘钥验证失败");
            }
            
            log.info("访问日志管理秘钥验证成功");
            return CommonResult.success(null, "秘钥验证成功");
            
        } catch (Exception e) {
            log.error("验证配置秘钥失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证Token
     */
    @ApiOperation("验证Token")
    @PostMapping("/auth/verify-token")
    public CommonResult<Object> verifyToken(@RequestBody Map<String, String> request) {
        try {
            String refreshCode = request.get("refreshCode");
            String accessToken = request.get("accessToken");
            
            if (refreshCode == null || refreshCode.trim().isEmpty()) {
                return CommonResult.failed("RefreshCode不能为空");
            }
            
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return CommonResult.failed("AccessToken不能为空");
            }
            
            // 验证Token - 这里简化验证逻辑，实际应该验证 refreshCode 和 accessToken 的关联性
            SecureTokenVo.ValidateResponse validateResponse = secureTokenService.validateAccessToken(accessToken.trim());
            boolean isValid = validateResponse != null && Boolean.TRUE.equals(validateResponse.getValid());
            
            if (!isValid) {
                log.warn("访问日志管理Token验证失败，RefreshCode: {}, AccessToken: {}", refreshCode, accessToken);
                return CommonResult.failed("Token验证失败");
            }
            
            log.info("访问日志管理Token验证成功，RefreshCode: {}", refreshCode);
            return CommonResult.success(null, "Token验证成功");
            
        } catch (Exception e) {
            log.error("验证Token失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    /**
     * 查询访问日志列表
     */
    @ApiOperation("查询访问日志列表")
    @GetMapping("/management/list")
    public CommonResult<Object> getAccessLogList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String requestUrl,
            @RequestParam(required = false) String requestMethod,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String requestIp,
            HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);
            if (userId != null && !userId.trim().isEmpty()) {
                params.put("userId", userId.trim());
            }
            if (userName != null && !userName.trim().isEmpty()) {
                params.put("userName", userName.trim());
            }
            if (requestUrl != null && !requestUrl.trim().isEmpty()) {
                params.put("requestUrl", requestUrl.trim());
            }
            if (requestMethod != null && !requestMethod.trim().isEmpty()) {
                params.put("requestMethod", requestMethod.trim());
            }
            if (status != null && !status.trim().isEmpty()) {
                params.put("status", status.trim());
            }
            if (startTime != null && !startTime.trim().isEmpty()) {
                params.put("startTime", startTime.trim());
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                params.put("endTime", endTime.trim());
            }
            if (requestIp != null && !requestIp.trim().isEmpty()) {
                params.put("requestIp", requestIp.trim());
            }

            // 调用服务查询数据
            Object result = queryAccessLogList(params);
            
            return CommonResult.success(result, "查询成功");
            
        } catch (Exception e) {
            log.error("查询访问日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取访问日志详情
     */
    @ApiOperation("获取访问日志详情")
    @GetMapping("/management/detail/{id}")
    public CommonResult<Object> getAccessLogDetail(@PathVariable String id, HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 调用服务获取详情
            Object result = getAccessLogDetailById(id);
            
            if (result == null) {
                return CommonResult.failed("日志记录不存在");
            }
            
            return CommonResult.success(result, "获取详情成功");
            
        } catch (Exception e) {
            log.error("获取访问日志详情失败", e);
            return CommonResult.failed("获取详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取统计数据
     */
    @ApiOperation("获取统计数据")
    @GetMapping("/management/statistics")
    public CommonResult<Object> getStatistics(HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 调用服务获取统计数据
            Object result = getAccessLogStatistics();
            
            return CommonResult.success(result, "获取统计数据成功");
            
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return CommonResult.failed("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控数据
     */
    @ApiOperation("获取监控数据")
    @GetMapping("/management/monitoring")
    public CommonResult<Object> getMonitoring(HttpServletRequest request) {
        try {
            // 验证AccessToken
            if (!validateAccessToken(request)) {
                return CommonResult.failed("AccessToken验证失败");
            }

            // 调用服务获取监控数据
            Object result = getAccessLogMonitoring();
            
            return CommonResult.success(result, "获取监控数据成功");
            
        } catch (Exception e) {
            log.error("获取监控数据失败", e);
            return CommonResult.failed("获取监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证AccessToken
     */
    private boolean validateAccessToken(HttpServletRequest request) {
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return false;
            }
            
            SecureTokenVo.ValidateResponse response = secureTokenService.validateAccessToken(accessToken.trim());
            return response != null && Boolean.TRUE.equals(response.getValid());
        } catch (Exception e) {
            log.error("验证AccessToken失败", e);
            return false;
        }
    }

    /**
     * 获取服务器信息
     */
    private Map<String, Object> getServerInfo(HttpServletRequest request) {
        try {
            Map<String, Object> serverInfo = new HashMap<>();
            
            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String contextPath = request.getContextPath();
            
            String requestUrl;
            if ((scheme.equals("http") && serverPort == 80) || (scheme.equals("https") && serverPort == 443)) {
                requestUrl = scheme + "://" + serverName + contextPath;
            } else {
                requestUrl = scheme + "://" + serverName + ":" + serverPort + contextPath;
            }
            
            serverInfo.put("requestUrl", requestUrl);
            
            return serverInfo;
        } catch (Exception e) {
            log.error("获取服务器信息失败", e);
            return null;
        }
    }

    /**
     * 生成错误页面
     */
    private String generateErrorPage(String errorMessage) {
        return String.format(
            "<!DOCTYPE html><html><head><title>错误</title></head><body>" +
            "<h1>页面加载失败</h1><p>%s</p>" +
            "<p><a href='javascript:history.back()'>返回上一页</a></p>" +
            "</body></html>",
            errorMessage
        );
    }

    // 以下方法需要根据实际的服务实现进行调用
    
    private Object queryAccessLogList(Map<String, Object> params) {
        // TODO: 调用实际的访问日志服务查询数据
        // 这里需要根据实际的 SystemRequestRecordService 实现
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("records", new java.util.ArrayList<>());
        mockResult.put("total", 0);
        mockResult.put("current", 1);
        mockResult.put("totalPages", 1);
        return mockResult;
    }
    
    private Object getAccessLogDetailById(String id) {
        // TODO: 调用实际的访问日志服务获取详情
        return new HashMap<>();
    }
    
    private Object getAccessLogStatistics() {
        // TODO: 调用实际的访问日志服务获取统计数据
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalRequests", 0);
        stats.put("successRequests", 0);
        stats.put("errorRequests", 0);
        stats.put("successRate", "0%");
        stats.put("avgResponseTime", 0);
        stats.put("slowRequests", 0);
        return stats;
    }
    
    private Object getAccessLogMonitoring() {
        // TODO: 调用实际的访问日志服务获取监控数据
        return new HashMap<>();
    }
}
