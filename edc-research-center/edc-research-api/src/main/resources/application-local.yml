############ 服务器配置 ############
server:
  # 应用服务端口
  port: 8000
  servlet:
    # API上下文路径
    context-path: /api
    # 字符编码配置
    encoding:
      enabled: true
      force: true
      charset: utf-8

  # Tomcat代理配置（用于负载均衡环境）
  tomcat:
    remote-ip-header: X-Forwarded-For
    port-header: X-Forwarded-Port
    protocol-header: X-Forwarded-Proto
    protocol-header-https-value: https
    # 连接超时配置
    connection-timeout: 30000
    # 最大连接数
    max-connections: 8192
    # 最大线程数
    max-threads: 200
    # 最小空闲线程数
    min-spare-threads: 10
    # 文件上传相关配置
    max-http-form-post-size: 20MB
    # 处理器缓存大小
    processor-cache: 200
    # 接受器线程数
    accept-count: 100


############ Spring框架配置 ############
spring:
  # 应用基本信息
  application:
    name: edc-research-master

  # 国际化配置
  messages:
    basename: config/messages
    encoding: UTF-8
    cache-duration: 3600

  # 主配置
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: false

  # 文件上传配置（修复配置路径）
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 200MB
      file-size-threshold: 500KB
      location: ${java.io.tmpdir}

  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
    servlet:
      load-on-startup: 1

  # JSON序列化配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
  # 多数据源配置
  datasource:
    dynamic:
      enabled: true
      primary: master
      strict: false
      # 连接池公共配置 - 优化支持高并发PDF导出
      hikari:
        minimum-idle: 10              # 增加最小空闲连接数
        maximum-pool-size: 100        # 增加最大连接数支持100并发
        idle-timeout: 300000          # 5分钟空闲超时
        max-lifetime: 1800000         # 30分钟最大生命周期
        connection-timeout: 60000     # 增加连接超时到60秒
        validation-timeout: 3000      # 减少验证超时
        leak-detection-threshold: 60000 # 连接泄漏检测
        auto-commit: true             # 自动提交
        connection-test-query: "SELECT 1" # 连接测试查询
      datasource:
        # 主数据库（MySQL）- 用户中心和基础数据
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************************************************='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'
          username: root
          password: Asd123456##
          hikari:
            pool-name: MasterHikariPool
            maximum-pool-size: 60       # 主库承担更多连接
            minimum-idle: 15            # 主库保持更多空闲连接
            connection-timeout: 60000   # 60秒连接超时

  # Redis缓存配置
  redis:
    host: 127.0.0.1
    port: 6379
    database: 1
    password:
    timeout: 30000

# 图片压缩缓存配置
image:
  compression:
    cache:
      # 是否启用图片压缩缓存
      enabled: true
      # 单个图片缓存过期时间（秒）- 24小时
      default-expire-time: 86400
      # 参与者图片缓存过期时间（秒）- 7天
      testee-cache-expire-time: 604800
      # 缓存键前缀
      cache-prefix: "image:compression:"
      # 参与者缓存键前缀
      testee-cache-prefix: "testee:image:compression:"

############ NoSQL数据库配置 ############
# MongoDB文档数据库配置
mongodb:
  enabled: false
  uri: *************:29018
  database: admin
  username: edc-reseacher
  password: K0qjYhod5qJvioZ2
  authenticationDatabase: admin

# Elasticsearch搜索引擎配置
elasticsearch:
  # 集群节点地址（多个用逗号分隔）
  hosts: **************:9200
  # 认证配置
  username: elastic1
  passwd: KYYPT#369admin
  apikey: MkVJc1k1QUI2XzY0dkhndWhkaUo6bVlwMHVXQ09ULWVVYnNtYkV0MU51UQ==
  # SSL配置
  enabled_ssl: true
  certificate_path: certificates/edc_http_ca.crt

################## 数据中心和专病库配置 ################
rdr-center:
  database_name: edc_disease_rdr
  patient_full_text_index: edc_xjbfy_full_text_index
  patient_visit_full_text_index: xjbfy_visit_full_text_index
  patient_join_visit_index: patient_join_visit_index

disease-xinjiang-center:
  database_name: edc_disease_xinjiang
  patient_full_text_index: xinjiang_patient_full_text_index
  patient_visit_full_text_index: xinjiang_visit_full_text_index
  patient_join_visit_index: xinjiang_patient_join_visit_index

disease-nxzbk:
  database_name: edc_nxzbk_disease
  patient_elastic_index: nxzbk_disease_data_index
  visit_elastic_index: nxzbk_visit_data_index
  visit_nested_elastic_index: visit_nested_elastic_index
  patient_full_text_index: edc_nxzbk_full_text_index
  patient_nested_text_index: edc_nxzbk_nested_text_index

################## 随机系统随机配置python脚本路径 ################
rcts:
  random_system_python_path: /webserver/weilong/miniconda3/bin/python3
  random_static_script_path: /webserver/weilong/script/staticRandomized.sh
  random_dynamic_script_path: /webserver/weilong/script/dynamicRandomized.sh
  random_bind_record_path: /webserver/weilong/record

################## mybatis配置 ################
############ 数据持久层配置 ############
# MyBatis配置
mybatis:
  # XML映射文件扫描路径
  mapper-locations:
    - classpath:dao/*.xml
    - classpath*:com/**/mapper/*.xml
    - classpath*:mapper/**/*.xml
  # MyBatis全局配置 - 优化性能
  configuration:
    # 驼峰命名转换
    map-underscore-to-camel-case: true
    # 空值处理
    call-setters-on-nulls: true
    # 延迟加载配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 缓存配置
    cache-enabled: true
    # 执行器类型优化
    default-executor-type: REUSE
    # 本地缓存作用域
    local-cache-scope: SESSION

# MyBatis-Plus增强配置
mybatis-plus:
  # XML映射文件扫描路径
  mapper-locations: classpath*:mapper/**/*.xml
  # 全局配置
  configuration:
    map-underscore-to-camel-case: true

############ 安全认证配置 ############
# JWT Token配置
jwt:
  # Token存储的请求头名称
  tokenHeader: Authorization
  # JWT加解密密钥（生产环境建议使用环境变量）
  secret: mis-admin-secret
  # Token过期时间（秒）3600=1小时
  expiration: 604800
  # Token前缀
  tokenHead: 'Bearer '
  # Token刷新时间（秒）1800=30分钟
  refresh-time: 1800

# 安全Token管理配置
secure-token:
  # 密钥配置
  secret-key: "EDC-SECURE-TOKEN-SECRET-KEY-2025"
  # Code有效期（秒）- 2分钟
  code-expiration: 120
  # AccessToken有效期（秒）- 1小时
  access-token-expiration: 3600
  # Redis键前缀
  redis-prefix: "secure:token:"
  # 是否启用
  enabled: true

# Redis数据管理配置
redis:
  management:
    # 管理密钥配置
    secret-key: "EDC-REDIS-MANAGEMENT-SECRET-2025"
    # 是否启用Redis管理功能
    enabled: true
    # 查询限制数量
    query-limit: 1000
    # 删除确认要求
    require-confirmation: true
    # 允许的操作类型
    allowed-operations: ["query", "delete"]
    # 禁止操作的key前缀
    forbidden-prefixes: ["system:", "security:", "session:"]


# 业务表管理配置
business:
  table:
    # 是否自动创建表
    auto-create: true
    # 启动时是否检查表
    check-on-startup: true
    # 创建失败时是否中止启动
    fail-on-error: false

############ 分页插件配置 ############
# PageHelper分页配置 - 优化性能
pagehelper:
  # 自动检测数据库方言
  auto-dialect: true
  # 关闭运行时自动检测，提升性能
  auto-runtime-dialect: false
  # 指定数据库方言
  helper-dialect: mysql
  # 分页合理化参数
  reasonable: true
  # 支持通过方法参数传递分页参数
  support-methods-arguments: true
  # 分页参数
  params: count=countSql
  # 当pageSize=0时是否返回全部结果
  page-size-zero: false
  # 关闭分页插件的自动检测，提升性能
  close-conn: true

############ 统一日志管理配置 ############
# 使用Spring Boot原生日志配置，支持在线实时查看和管理
logging:
  # 日志级别配置
  level:
    root: INFO
    # 业务模块日志级别（开发环境使用DEBUG便于调试）
    com.haoys.user: INFO
    com.haoys.rdr: INFO
    com.haoys.disease: INFO
    com.haoys.xinjiang: INFO
    # 第三方组件日志级别优化（减少无用日志）
    org.springframework.data.convert.CustomConversions: ERROR
    org.mongodb.driver.connection: ERROR
    org.springframework.security: WARN
    org.springframework.web: INFO
    org.springframework.boot: INFO
    # 数据库相关日志
    com.zaxxer.hikari: WARN
    # 缓存和搜索引擎日志
    io.lettuce.core: WARN
    org.elasticsearch.client: WARN
    # HTTP客户端日志
    org.apache.http: WARN

  # 日志文件配置（统一存放便于管理）
  file:
    # 主日志文件路径
    name: /Users/<USER>/local/edc-logs/edc-research-project.log

  # 日志输出格式配置
  pattern:
    # 控制台输出格式（彩色，便于开发调试）
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n"
    # 文件输出格式（详细信息，便于生产问题排查）
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}:%line] [TraceId:%X{traceId:-}] - %msg%n"
    # 滚动文件命名模式（按日期和大小分割）
    rolling-file-name: /Users/<USER>/local/edc-logs/edc-research-project-%d{yyyy-MM-dd}.%i.log

  # 日志轮转配置（按照最佳实践进行优化）
  logback:
    rollingpolicy:
      # 单个文件最大大小（100KB，便于快速查看和传输）
      max-file-size: 10MB
      # 保留的历史文件数量（50个，确保有足够的历史记录）
      max-history: 50
      # 总文件大小限制（50MB，防止磁盘空间耗尽）
      total-size-cap: 500MB
      # 启动时清理过期文件
      clean-history-on-start: true
      # 文件压缩（启用压缩节省空间）
      compress: false

  # 日志配置组（便于批量管理相关日志级别）
  group:
    # 业务日志组
    business: com.haoys.user,com.haoys.mis,com.haoys.rdr,com.haoys.disease,com.haoys.xinjiang
    # 数据库日志组
    database: org.hibernate,com.zaxxer.hikari,org.springframework.jdbc
    # 缓存日志组
    cache: io.lettuce,org.springframework.cache
    # 安全日志组
    security: org.springframework.security,com.haoys.user.security

############ 安全路径白名单配置 ############
secure:
  ignored:
    urls:
      # ========== 系统基础资源 ==========
      # 静态资源文件
      - /**/*.js
      - /**/*.css
      - /**/*.png
      - /**/*.jpeg
      - /**/*.jpg
      - /**/*.ico
      - /**/*.html
      - /**/*.md
      - /**/*.sh
      - /static/**
      - /file/**

      # ========== API文档相关 ==========
      # Swagger文档接口
      - /swagger-ui.html
      - /swagger-resources/**
      - /swagger/**
      - /doc.html
      - /**/v2/api-docs
      - /webjars/springfox-swagger-ui/**

      # ========== 监控和管理 ==========
      # 应用监控端点
      - /actuator/**
      # 数据库监控（如果启用Druid）
      - /druid/**

      # ========== 访问日志管理 ==========
      # 访问日志管理页面
      - /access-log/management/**
      # 访问日志认证接口
      - /access-log/auth/**

      # ========== 定时任务管理 ==========
      # 定时任务管理页面
      - /quartz/job/management/**


      # 系统监控页面
      - /monitor/**
      - /templates/system-monitor/**
      # 系统监控API接口
      - /system/monitor/**
      # 安全令牌验证
      - /secure/token/validate

      # ========== 用户认证相关 ==========
      # 管理员登录注册（核心认证接口）
      - /admin/login
      - /admin/register
      - /admin/savePatientUser
      - /admin/verificationCodeLogin
      - /admin/updateTesteePassword
      - /admin/getUserBaseInfoByUserName
      - /admin/checkVerificationCodeByAccount
      - /admin/findUserPassword
      - /admin/getSystemUserInfoByTokenValue
      - /admin/checkToken
      - /admin/actUser
      - /admin/externalSystemRegisterUser
      - /admin/getUserLoginTokenByAccessToken
      - /admin/getUserBaseInfoByAccountName
      - /admin/getUserLoginResult
      - /admin/externalSystemUserLogin
      - /admin/getSystemUserActiveStatus

      # 移动端用户（移动端认证）
      - /mobileUser/register
      - /mobileUser/checkVerification
      - /mobileLogin/**

      # 验证码服务（登录必需）
      - /captcha/**

      # ========== 消息通知服务 ==========
      # 短信和邮件服务（验证码发送）
      - /message/sendMobileMessageCode
      - /message/sendMessageCodeByEmail

      # ========== 文件服务 ==========
      # 文件上传接口
      - /minio/upload
      - /postFileUpload

      # ========== 第三方集成接口 ==========
      # 报表系统集成
      - /thirdPartyApi/getAjreportLoginToken
      - /thirdPartyApi/getUserLoginInfo

      # ========== 数据同步接口 ==========
      # 患者数据同步（外部系统调用）
      - /patients/**
      - /patient-data/**
      - /patientSync/**
      - /xinjiang/patient-data/**
      - /xinjiang/patientSync/**

      # ========== 系统配置接口 ==========
      # 组织信息配置（系统初始化）
      - /sysOrgInfo/add
      - /sysOrgInfo/list
      - /sysOrgInfo/getThirdSystemOrgInfoByName

      # ========== 数据展示接口 ==========
      # 数据大屏（公开展示，无需认证）
      - /rdr-bigScreen/**

      # ========== 外部系统集成 ==========
      # 科研系统Token获取
      - /keYanSystem/getToken/**

      # ========== 安全Token管理接口 ==========
      # 安全Token相关接口
      - /secure/token/**

      # ========== Redis数据管理接口 ==========
      # Redis管理静态页面
      - /redis-management/**
      # Redis管理API接口（需要AccessToken认证）
      - /redis/management/**

      # ========== AI智能对话接口 ==========
      # AI聊天相关接口（开发环境开放访问）
      - /ai/chat/**
      - /ai/config/**

      # ========== 日志服务接口 ==========
      # 日志查看器相关接口（旧路径）
      - /logs/health
      - /logs/viewer
      # 新的日志管理接口（使用accessToken验证）
      - /log-management/viewer
      - /log-management/viewer/**
      - /log-management/files/**
      - /log-management/download/**
      - /log-management/tail/**
      - /log-management/content/**
      # 日志管理静态页面
      - /log-management/**
      # WebSocket连接端点
      - /websocket/**
      - /backStage/**

############ 第三方服务配置 ############
# 腾讯云短信服务配置
sms:
  # 腾讯云API密钥
  secretId: AKIDTuEPYZSBlDXghbMxQlwysdGnBRYWKzVt
  secretKey: JTUL2azGWaVIvOisshMrCNNDuRp4A8wa
  # 短信应用配置
  sdkAppId: **********
  signName: 北京健康在线
  # 短信模板配置
  templateId: 1448295          # 登录验证码模板
  inviteTemplateId: 1966799    # 邀请用户模板
  # 邮件主题
  subject: 好医生集团科研协作平台


# 百度AI服务配置
baiduyun:
  # OCR文字识别服务
  ocr:
    APIKey: hwDGxThlKUAQSXFVukYnejLP
    SecretKey: qgtgY7WZRGlZCOTPFthqGwVohqlCEocS
    timeout: 30000
    retry-times: 3
  # NLP自然语言处理服务
  nlp:
    APIKey: f1uF2nBglEhFvB6TfTa4yuzG
    SecretKey: 4EDGwGu0V3YEzPH3VFHiqdLQx2bmlBhM
    timeout: 30000
    retry-times: 3

############ 日志查看器配置 ############
# 日志查看器安全配置
log:
  viewer:
    # 访问令牌（生产环境请使用更安全的令牌）
    access-token: edc-log-viewer-2025-dev
    # 令牌有效期（分钟）
    token-expire-minutes: 120
    # 最大连接数限制
    max-connections: 10
    # 日志推送间隔（秒）
    push-interval: 3

############ WebSocket日志配置 ############
# WebSocket连接和日志输出控制配置
websocket:
  # 服务器配置（支持远程部署）
  server:
    # WebSocket服务器地址（开发环境使用localhost）
    address: localhost
  # 日志输出控制配置
  log:
    # 是否启用WebSocket相关日志输出
    enabled: true
    # 是否启用在线日志输出功能
    online-output-enabled: true
    # 是否启用日志打印功能
    print-enabled: true
    # 日志级别（DEBUG, INFO, WARN, ERROR）
    level: INFO
    # 扫描间隔（秒）
    scan-interval: 1
    # 最大行数
    max-lines: 1000
    # ========== 智能日志提取配置 ==========
    # 是否启用智能日志内容提取（默认false，显示完整日志）
    smart-extract-enabled: false
    # 是否显示完整日志内容（默认true，优先级高于智能提取）
    show-full-content: true
    # 是否只提取日志内容部分（默认false，当智能提取启用时生效）
    extract-content-only: false
    # ========== WebSocket日志测试任务配置 ==========
    # 是否启用WebSocket日志测试任务（仅在local环境启用）
    test-task-enabled: false

############ 事务测试配置 ############
# 全局事务测试配置
transaction:
  test:
    # 是否启用事务测试
    enabled: true
    # 是否输出事务测试日志
    log-enabled: true
    # 事务测试日志级别
    log-level: INFO
    # 是否在启动时自动运行测试
    auto-run-on-startup: true
    # 测试结果输出格式（SIMPLE, DETAILED）
    output-format: DETAILED

############ 安全防护配置 ############
# XSS攻击防护配置（使用增强版XSS过滤器）
xss:
  # 启用XSS过滤
  enabled: true
  # 排除不需要过滤的接口（支持Ant路径模式）
  excludes: /api/upload/**,/api/file/**,/doc.html,/swagger-ui/**,/captcha/**,/actuator/**
  # 需要过滤的URL模式
  urlPatterns: /api/**
  # 严格模式（启用更严格的XSS检测）
  strictMode: true
  # 最大输入长度限制
  maxInputLength: 10000

############ 平台服务配置 ############
platform:
  # 对象存储服务配置
  oss:
    # OSS类型（1=阿里云OSS，2=腾讯云COS，3=本地存储）
    ossType: 1
    # OSS服务端点
    endpoint: 12
    # 访问密钥
    accessKeyId: NpOWhsXxLniKSnT_VbilS8K3iOyqMVqE82jHUQx1
    accessKeySecret: Rx6t35hhyTz9koX7_JV_Y2__AMZTffENOqEJFFez
    # 存储桶名称
    bucketName: hysh-dev
    # 根路径
    rootPath: edc-research-project
    # 访问域名
    domain: http://hysh-dev.haoyisheng.com.cn
    # 本地上传目录
    uploadFolder: /Users/<USER>/webserver/upload/
    # 文件访问路径模式
    accessPathPattern: /file/**
    # 文件预览URL
    viewUrl: http://localhost:8000/api/
    # 文件大小限制（MB）
    maxFileSize: 100
    # 允许的文件类型
    allowedTypes: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar

  # API文档配置
  swagger:
    # 启用Swagger文档
    enable: true
    # 文档标题
    title: EDC科研协作平台API文档
    # 文档描述
    description: 提供完整的API接口文档和在线测试功能
    # 版本号
    version: 2.0.0
    # 联系信息
    contact:
      name: 技术支持团队
      email: <EMAIL>

############ AI智能服务配置 ############
# 大语言模型服务配置
llm:
  # 聊天机器人服务
  chatbot_url: http://**************:5000/chat
  # 备用聊天机器人服务
  chatbot_backup_url: https://mbgldev.haoyisheng.com/optimus/aiBoat/chat
  # 数据分析服务
  data_analysis_url: http://**************:5001/data_analysis
  # 报告生成服务
  report_url: http://**************:5001/report
  # 可视化展示服务
  view_url: http://**************/
  # 服务超时时间（毫秒）
  timeout: 60000
  # 重试次数
  retry-times: 3
  # 启用状态
  enabled: true

############ AI聊天系统配置 ############
edc:
  ai:
    chat:
      # 是否启用AI聊天功能
      enabled: true
      # 默认模型类型
      default-model-type: qwen
      # 默认模型名称
      default-model-name: qwen-turbo
      # 会话超时时间(分钟)
      session-timeout: 60
      # 最大会话数量(每个用户)
      max-sessions-per-user: 10
      # 最大消息长度
      max-message-length: 10000
      # 最大文件大小(MB)
      max-file-size: 50
      # 支持的文件类型
      supported-file-types:
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp
        - pdf
        - doc
        - docx
        - txt
        - md
        - xls
        - xlsx
        - csv
        - ppt
        - pptx
      # 流式响应配置
      stream:
        enabled: true
        timeout: 30
        heartbeat-interval: 5
      # 重试配置
      retry:
        max-attempts: 3
        retry-delay: 1000
        retry-multiplier: 2.0
      # 限流配置
      rate-limit:
        enabled: true
        requests-per-minute: 20
        tokens-per-hour: 100000
        max-cost-per-day: 10.0

############ 外部系统集成配置 ############
# 科研数据分析服务
thirdservice:
  # 科研服务URL
  research_url: https://src-demo.haoyisheng.info/sci
  # 同步类型
  sync_type: shiyong
  # 连接超时时间
  timeout: 30000
  # 启用状态
  enabled: true

# BDP用户同步服务配置
bdpconfig:
  # BDP登录服务URL
  bdp_url: http://**************:8380/bdp-web/login/loginByPhoneForOrigin
  # BDP用户信息获取URL
  bdp_get_user_url: http://**************:7001/user/getUserInfoByUserId
  # BDP票据验证URL
  bdp_check_ticket_url: http://**************:7001/sso/checkTicket
  # 启用BDP同步
  enable: true
  # 同步间隔（分钟）
  sync_interval: 30
  # 连接超时时间
  timeout: 15000


############ 业务功能配置 ############

# 用户管理配置
user:
  # 用户首次登录激活页面URL
  act-page: http://210.12.60.60:8002/disease-dev/#/
  # 用户会话超时时间（分钟）
  session-timeout: 30
  # 最大登录失败次数
  max-login-attempts: 5
  # 账户锁定时间（分钟）
  lockout-duration: 15

# 密码策略配置
pwd:
  # 是否启用密码策略
  open-pwd-config: false
  # 密码最小长度
  min-length: 8
  # 密码最大长度
  max-length: 20
  # 是否要求包含数字
  require-digit: true
  # 是否要求包含字母
  require-letter: true
  # 是否要求包含特殊字符
  require-special-char: false

# 滑动验证码配置
aj:
  captcha:
    jigsaw: classpath:static/images/jigsaw
    pic-click: classpath:static/images/pic-click
    # 缓存类型（local/redis）
    cache-type: redis
    # 本地缓存阈值
    cache-number: 1000
    # 定时清除过期缓存间隔（秒）
    timing-clear: 180
    # 验证码类型
    type: default
    # 水印文字
    water-mark: SRC-EDC
    # 滑动拼图允许误差偏移量（像素）
    slip-offset: 100
    # AES加密坐标
    aes-status: true
    # 滑动干扰项级别（0/1/2）
    interference-options: 0
    # 启用历史数据清理
    history-data-clear-enable: true
    # 启用接口请求频率限制
    req-frequency-limit-enable: true
    # 验证失败锁定阈值
    req-get-lock-limit: 5
    # 锁定时间间隔（秒）
    req-get-lock-seconds: 60
    # 各接口每分钟请求数限制
    req-get-minute-limit: 30
    req-check-minute-limit: 60
    req-verify-minute-limit: 60

# 表单审核功能配置
form:
  # 启用表单审核
  audit: true

############ 系统监控配置 ############
# 异常告警配置（简化版）
exception:
  alert:
    # 启用异常告警
    enabled: true
    # 异常阈值（每分钟）
    threshold-per-minute: 10
    # 告警静默期（分钟）
    silence-period: 5
    # 告警接收人配置
    contacts:
      # 手机号（多个用逗号分隔）
      phones: "13341080362"
      # 邮箱（多个用逗号分隔）
      emails: "<EMAIL>"

# 安全审计配置（简化版）
security:
  audit:
    # 启用安全审计
    enabled: true
    # 记录级别（DEBUG, INFO, WARN, ERROR）
    level: INFO
    # 数据保留天数
    retention-days: 30
    # 存储类型（database, file）
    storage-type: database

############ 系统性能配置 ############
# 线程池配置
thread-pool:
  # 核心线程数
  core-size: 10
  # 最大线程数
  max-size: 50
  # 队列容量
  queue-capacity: 200
  # 线程空闲时间（秒）
  keep-alive-seconds: 60
  # 线程名前缀
  thread-name-prefix: "edc-async-"

# 缓存配置
cache:
  # 默认过期时间（秒）
  default-ttl: 3600


############ 系统许可证配置 ############
system:
  license:
    # 管理员访问令牌（用于远程管理许可证）
    access-token: edc-license-admin-2025-dev
    # 默认许可证有效期（天数）
    default-expire-days: 365
    # 许可证验证间隔（分钟）
    validation-interval: 60
    # 最大验证失败次数
    max-validation-failures: 5
    # 是否开启证书限制使用
    certificate-restriction-enabled: false

############ 性能监控配置 ############
performance:
  monitor:
    # 启用性能监控
    enabled: true
    # 慢方法阈值（毫秒）
    slow-method-threshold: 1000
    # 慢SQL阈值（毫秒）
    slow-sql-threshold: 500
    # 非常慢SQL阈值（毫秒）
    very-slow-sql-threshold: 2000
    # 统计信息保留时间（小时）
    stats-retention-hours: 24


############ 系统监控配置 ############

############ 系统访问日志配置 ############
request:
  access:
    log:
      enabled: true                    # 是否启用系统日志记录
      async-enabled: true              # 是否启用异步日志记录
      save-request-data: true          # 是否记录请求参数
      save-response-data: true         # 是否记录响应数据
      save-device-info: true           # 是否记录设备信息
      save-location-info: true         # 是否记录地理位置信息
      max-param-length: 2000           # 最大参数长度限制
      max-response-length: 5000        # 最大响应结果长度限制
      slow-request-threshold: 5000     # 慢请求阈值（毫秒）
      data-retention-days: 90          # 数据保留天数
      auto-cleanup-enabled: true       # 是否启用数据自动清理
      async-queue-size: 1000           # 异步队列大小
      async-thread-pool-size: 5        # 异步线程池大小
      filter-static-resources: true    # 是否过滤静态资源
      filter-health-check: true        # 是否过滤健康检查请求
      sensitive-keys:                  # 敏感参数关键字列表
        - password
        - pwd
        - token
        - secret
        - key
        - authorization
        - passwd
        - credential
        - auth
        - sign
        - signature
      exclude-url-patterns:            # 需要过滤的URL模式
        - /actuator/**
        - /health/**
        - /static/**
        - /css/**
        - /js/**
        - /images/**
        - /favicon.ico
        - /webjars/**
        - "/*.css"
        - "/*.js"
        - "/*.png"
        - "/*.jpg"

      management:
        # 管理密钥配置
        secret-key: "EDC-ACCESS-LOG-MANAGEMENT-SECRET-2025"
        # 是否启用访问日志管理功能
        enabled: true
access:
  monitor:
    # 是否启用系统监控功能（总开关）
    enabled: true
    # 访问日志监控配置
    enable-access-log: true
    # 访问日志保留天数（超过此天数的日志将被自动清理）
    access-log-retention-days: 30
    # 在线用户监控配置
    enable-online-user: true
    # 在线用户超时时间（分钟），超过此时间未活动的用户将被标记为离线
    online-user-timeout-minutes: 120
    # 异常日志监控配置
    enable-exception-log: true
    # 统计数据配置
    enable-auto-statistics: true
    # 统计数据生成时间（cron表达式）- 每天凌晨1点自动生成前一天的统计数据
    statistics-cron: "0 0 1 * * ?"
    # 实时监控配置
    enable-real-time-monitor: true
    # 实时监控数据刷新间隔（秒）
    real-time-refresh-interval: 30