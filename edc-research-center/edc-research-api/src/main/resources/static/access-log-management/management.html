<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统访问日志管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-title {
            text-align: left;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 0;
        }

        .token-status {
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .token-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .token-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        .status-dot.warning {
            background: #fbbf24;
        }

        .status-dot.danger {
            background: #ef4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .token-expire {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 13px;
            opacity: 0.9;
        }

        .expire-time {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #fbbf24;
        }

        .logout-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .auth-section {
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
        }

        .auth-form {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            display: none;
        }

        .alert.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .main-content {
            display: none;
            padding: 40px;
        }

        .tabs {
            margin-bottom: 30px;
        }

        .tab-header {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 20px;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            font-size: 16px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .search-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .search-group {
            flex: 1;
            min-width: 200px;
        }

        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .search-group input,
        .search-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .btn-search {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn-reset {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .detail-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 20px;
            border-radius: 10px;
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .detail-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .detail-item label {
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 5px;
        }

        .detail-item span {
            color: #666;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .json-container {
            position: relative;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 5px;
            max-height: 300px;
            overflow-y: auto;
        }

        .json-content {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .copy-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .copy-btn:hover {
            background: #0056b3;
        }

        .copy-btn.copied {
            background: #28a745;
        }

        .error-text {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .monitoring-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }

        .monitoring-card h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }

        .metric-value.success {
            color: #28a745;
        }

        .metric-value.warning {
            color: #ffc107;
        }

        .metric-value.danger {
            color: #dc3545;
        }

        .metric-value.primary {
            color: #007bff;
        }

        .health-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .health-indicator.healthy {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .health-indicator.unhealthy {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .refresh-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .refresh-btn:hover {
            background: #138496;
        }

        /* 动画样式 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }

        /* 强制刷新按钮样式 */
        .refresh-btn[style*="background: #dc3545"]:hover {
            background: #c82333 !important;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-title {
                text-align: center;
            }

            .token-status {
                flex-direction: column;
                gap: 15px;
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-title">
                    <h1>系统访问日志管理</h1>
                    <p>安全访问 · 实时监控 · 数据分析</p>
                </div>
                <div class="token-status" id="tokenStatus" style="display: none;">
                    <div class="token-info">
                        <div class="token-indicator">
                            <span class="status-dot" id="statusDot"></span>
                            <span class="status-text" id="statusText">已认证</span>
                        </div>
                        <div class="token-expire">
                            <span class="expire-label">剩余时间：</span>
                            <span class="expire-time" id="expireTime">--:--:--</span>
                        </div>
                    </div>
                    <button class="logout-btn" onclick="logout()" title="退出登录">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        退出
                    </button>
                </div>
            </div>
        </div>

        <!-- 认证区域 -->
        <div class="auth-section" id="authSection">
            <div class="auth-form" id="secretKeyForm">
                <h3>第一步：秘钥验证</h3>
                <div class="alert" id="secretAlert"></div>
                <div class="form-group">
                    <label for="secretKey">请输入配置秘钥：</label>
                    <input type="password" id="secretKey" placeholder="请输入秘钥">
                </div>
                <button class="btn" onclick="verifySecretKey()">验证秘钥</button>
            </div>

            <div class="auth-form" id="tokenForm" style="display: none;">
                <h3>第二步：获取访问令牌</h3>
                <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-size: 14px;">
                    <strong>🔑 访问令牌获取说明</strong><br>
                    请联系系统管理员获取有效的验证码和刷新码，或使用generateCode方法生成
                </div>
                <div class="alert" id="tokenAlert"></div>
                <div class="form-group">
                    <label for="code">验证码 (Code)：</label>
                    <input type="text" id="code" placeholder="请输入验证码">
                </div>
                <div class="form-group">
                    <label for="refreshCode">刷新码 (RefreshCode)：</label>
                    <input type="text" id="refreshCode" placeholder="请输入刷新码">
                </div>
                <button class="btn" onclick="getAccessToken()">获取访问令牌</button>
            </div>

            <div class="auth-form" id="validateForm" style="display: none;">
                <h3>第三步：验证访问令牌</h3>
                <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-size: 14px;">
                    <strong>🚀 访问令牌验证</strong><br>
                    系统正在验证您的访问令牌，验证成功后将自动跳转到访问日志管理页面
                </div>
                <div class="loading" id="loading" style="text-align: center; padding: 20px;">
                    <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 10px;">正在验证访问令牌...</p>
                </div>
                <div id="success-message" style="display: none; text-align: center; color: #28a745;">
                    <p>✅ 验证成功！正在跳转到访问日志管理页面...</p>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content" id="mainContent">
            <div class="tabs">
                <div class="tab-header">
                    <button class="tab-btn active" onclick="switchTab('logs')">访问日志</button>
                    <button class="tab-btn" onclick="switchTab('statistics')">统计分析</button>
                    <button class="tab-btn" onclick="switchTab('monitoring')">实时监控</button>
                </div>

                <!-- 访问日志标签页 -->
                <div class="tab-content active" id="logs-tab">
                    <div class="alert" id="logs-alert"></div>
                    
                    <div class="search-form">
                        <div class="search-row">
                            <div class="search-group">
                                <label>用户ID</label>
                                <input type="text" id="userId" placeholder="请输入用户ID">
                            </div>
                            <div class="search-group">
                                <label>用户名</label>
                                <input type="text" id="userName" placeholder="请输入用户名" value="">
                            </div>
                            <div class="search-group">
                                <label>请求URL</label>
                                <input type="text" id="requestUrl" placeholder="请输入请求URL">
                            </div>
                            <div class="search-group">
                                <label>请求方法</label>
                                <select id="requestMethod">
                                    <option value="">全部</option>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-row">
                            <div class="search-group">
                                <label>状态</label>
                                <select id="status">
                                    <option value="">全部</option>
                                    <option value="1">成功</option>
                                    <option value="0">失败</option>
                                </select>
                            </div>
                            <div class="search-group">
                                <label>开始时间</label>
                                <input type="datetime-local" id="startTime">
                            </div>
                            <div class="search-group">
                                <label>结束时间</label>
                                <input type="datetime-local" id="endTime">
                            </div>
                            <div class="search-group">
                                <label>IP地址</label>
                                <input type="text" id="requestIp" placeholder="请输入IP地址">
                            </div>
                        </div>
                        <div class="search-actions">
                            <button class="btn-reset" onclick="resetSearch()">重置</button>
                            <button class="btn-search" onclick="searchLogs()">查询</button>
                        </div>
                    </div>

                    <div id="logsContent">
                        <div class="loading">正在加载数据...</div>
                    </div>

                    <div class="pagination" id="logsPagination" style="display: none;">
                        <!-- 分页控件将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 统计分析标签页 -->
                <div class="tab-content" id="statistics-tab">
                    <div class="alert" id="statistics-alert"></div>
                    <div id="statisticsContent">
                        <div class="loading">正在加载统计数据...</div>
                    </div>
                </div>

                <!-- 实时监控标签页 -->
                <div class="tab-content" id="monitoring-tab">
                    <div class="alert" id="monitoring-alert"></div>
                    <div id="monitoringContent">
                        <div class="loading">正在加载监控数据...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="detail-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>访问日志详情</h3>
                <span class="close" onclick="closeDetailModal()">&times;</span>
            </div>
            <div id="detailContent">
                <!-- 详情内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script src="access-log-management.js"></script>
</body>
</html>
