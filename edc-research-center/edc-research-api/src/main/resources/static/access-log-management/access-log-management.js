/**
 * 系统访问日志管理 JavaScript
 * 提供完整的日志查询、统计和监控功能
 */

// 全局变量
let currentPage = 1;
let currentPageSize = 20;
let accessToken = '';
let isAuthenticated = false;
let tokenExpireTime = 0;
let tokenStatusTimer = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('系统访问日志管理页面初始化...');
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 检查是否已经认证
    const savedToken = sessionStorage.getItem('accessLogToken');
    const tokenExpire = sessionStorage.getItem('accessLogTokenExpire');

    if (savedToken && tokenExpire && Date.now() < parseInt(tokenExpire)) {
        accessToken = savedToken;
        tokenExpireTime = parseInt(tokenExpire);
        isAuthenticated = true;
        showMainContent();
        // 默认加载访问日志数据
        loadAccessLogs();
    }

    // 绑定回车键事件
    document.getElementById('secretKey').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifySecretKey();
        }
    });

    const codeInput = document.getElementById('code');
    const refreshCodeInput = document.getElementById('refreshCode');

    if (codeInput) {
        codeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }

    if (refreshCodeInput) {
        refreshCodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }
}

/**
 * 获取API基础URL
 */
function getApiBaseUrl() {
    if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
        return window.SERVER_CONFIG.requestUrl + '/api';
    }
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}/api`;
}

/**
 * 显示提示信息
 */
function showAlert(containerId, message, type = 'danger') {
    const alertContainer = document.getElementById(containerId);
    if (alertContainer) {
        alertContainer.className = `alert ${type}`;
        alertContainer.textContent = message;
        alertContainer.style.display = 'block';
        
        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                alertContainer.style.display = 'none';
            }, 3000);
        }
    }
}

/**
 * 验证秘钥
 */
async function verifySecretKey() {
    const secretKey = document.getElementById('secretKey').value.trim();
    
    if (!secretKey) {
        showAlert('secretAlert', '请输入秘钥');
        return;
    }
    
    try {
        const response = await fetch(`${getApiBaseUrl()}/access-log/auth/verify-secret`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ secretKey })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('secretAlert', '秘钥验证成功，请获取访问令牌', 'success');
            document.getElementById('secretKeyForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        } else {
            showAlert('secretAlert', result.message || '秘钥验证失败');
        }
    } catch (error) {
        console.error('秘钥验证失败:', error);
        showAlert('secretAlert', '网络错误，请稍后重试');
    }
}

/**
 * 获取访问令牌
 */
async function getAccessToken() {
    const code = document.getElementById('code').value.trim();
    const refreshCode = document.getElementById('refreshCode').value.trim();

    if (!code || !refreshCode) {
        showAlert('tokenAlert', '请输入验证码和刷新码');
        return;
    }

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/getAccessToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: code,
                refreshCode: refreshCode
            })
        });

        const result = await response.json();

        if (result.code === 200 && result.data && result.data.accessToken) {
            accessToken = result.data.accessToken;
            console.log('AccessToken获取成功:', accessToken);

            // 保存token到sessionStorage
            sessionStorage.setItem('accessLogToken', accessToken);
            if (result.data.expiresIn) {
                tokenExpireTime = Date.now() + (result.data.expiresIn * 1000);
                sessionStorage.setItem('accessLogTokenExpire', tokenExpireTime);
            }

            showAlert('tokenAlert', '访问令牌获取成功！', 'success');

            // 显示第三步验证界面
            document.getElementById('tokenForm').style.display = 'none';
            document.getElementById('validateForm').style.display = 'block';

            // 自动验证token
            setTimeout(validateAccessToken, 1000);
        } else {
            showAlert('tokenAlert', result.message || '获取访问令牌失败');
        }
    } catch (error) {
        console.error('获取访问令牌失败:', error);
        showAlert('tokenAlert', '网络错误，请稍后重试');
    }
}

/**
 * 验证访问令牌
 */
async function validateAccessToken() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('success-message').style.display = 'none';

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/validate?accessToken=${encodeURIComponent(accessToken)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        document.getElementById('loading').style.display = 'none';

        if (result.code === 200 && result.data && result.data.valid) {
            console.log('Token验证成功，剩余时间:', result.data.remainingTime, '秒');

            // 显示成功消息
            document.getElementById('success-message').style.display = 'block';

            isAuthenticated = true;

            // 延迟跳转到管理页面
            setTimeout(() => {
                showMainContent();
                loadAccessLogs();
            }, 2000);
        } else {
            showAlert('tokenAlert', result.message || 'Token验证失败');
            // 重新显示token获取表单
            document.getElementById('validateForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        }
    } catch (error) {
        console.error('Token验证失败:', error);
        document.getElementById('loading').style.display = 'none';
        showAlert('tokenAlert', '网络错误，请稍后重试');
        // 重新显示token获取表单
        document.getElementById('validateForm').style.display = 'none';
        document.getElementById('tokenForm').style.display = 'block';
    }
}

/**
 * 显示主要内容
 */
function showMainContent() {
    document.getElementById('authSection').style.display = 'none';
    document.getElementById('mainContent').style.display = 'block';
    document.getElementById('tokenStatus').style.display = 'flex';

    // 启动Token状态监控
    startTokenStatusMonitor();
}

/**
 * 切换标签页
 */
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // 激活当前标签
    event.target.classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');

    // 如果切换到非监控标签页，停止实时监控
    if (tabName !== 'monitoring' && isMonitoringActive) {
        stopRealTimeMonitoring();
    }

    // 根据标签页加载对应数据
    switch(tabName) {
        case 'logs':
            loadAccessLogs();
            break;
        case 'statistics':
            loadStatistics();
            break;
        case 'monitoring':
            loadMonitoring();
            break;
    }
}

/**
 * 加载访问日志数据
 */
async function loadAccessLogs() {
    try {
        showLoading('logsContent');
        
        const params = {
            pageNum: currentPage,
            pageSize: currentPageSize,
            userId: document.getElementById('userId').value.trim(),
            userName: document.getElementById('userName').value.trim(),
            requestUrl: document.getElementById('requestUrl').value.trim(),
            requestMethod: document.getElementById('requestMethod').value,
            status: document.getElementById('status').value,
            startTime: document.getElementById('startTime').value,
            endTime: document.getElementById('endTime').value,
            requestIp: document.getElementById('requestIp').value.trim()
        };
        
        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });
        
        const url = `${getApiBaseUrl()}/access-log/management/list`;
        const response = await makeApiRequest(url, {
            method: 'GET',
            data: params
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            displayAccessLogs(result.data);
            showAlert('logs-alert', '数据加载成功', 'success');
        } else {
            showAlert('logs-alert', result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载访问日志失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示加载状态
 */
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }
}

/**
 * 发起API请求
 */
async function makeApiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'X-Access-Token': accessToken
        }
    };
    
    if (options.method === 'GET' && options.data) {
        const params = new URLSearchParams(options.data);
        url += '?' + params.toString();
        delete options.data;
    } else if (options.data) {
        defaultOptions.body = JSON.stringify(options.data);
    }
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    return fetch(url, finalOptions);
}

/**
 * 显示访问日志数据
 */
function displayAccessLogs(data) {
    const container = document.getElementById('logsContent');
    const pagination = document.getElementById('logsPagination');
    
    if (!data || !data.records || data.records.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无数据</div>';
        pagination.style.display = 'none';
        return;
    }
    
    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>用户</th>
                    <th>请求方法</th>
                    <th>请求URL</th>
                    <th>状态</th>
                    <th>响应时间</th>
                    <th>IP地址</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.records.forEach(record => {
        const statusClass = record.isSuccess ? 'status-success' : 'status-error';
        const statusText = record.isSuccess ? '成功' : '失败';
        const responseTime = record.responseTime ? record.responseTime + 'ms' : '-';
        const requestTime = record.requestStartTime ? new Date(record.requestStartTime).toLocaleString() : '-';
        
        html += `
            <tr>
                <td>${requestTime}</td>
                <td>${record.userName || '-'}</td>
                <td>${record.requestMethod || '-'}</td>
                <td title="${record.requestUrl || '-'}">${truncateText(record.requestUrl || '-', 50)}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${responseTime}</td>
                <td>${record.requestIp || '-'}</td>
                <td>
                    <button class="btn-search" onclick="viewLogDetail('${record.id}')">详情</button>
                </td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    container.innerHTML = html;
    
    // 显示分页控件
    if (data.totalPages > 1) {
        displayPagination(data);
        pagination.style.display = 'flex';
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 截断文本
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

/**
 * 显示分页控件
 */
function displayPagination(data) {
    const pagination = document.getElementById('logsPagination');
    let html = '';
    
    // 上一页
    if (data.current > 1) {
        html += `<button onclick="changePage(${data.current - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, data.current - 2);
    const endPage = Math.min(data.totalPages, data.current + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === data.current ? 'active' : '';
        html += `<button class="${activeClass}" onclick="changePage(${i})">${i}</button>`;
    }
    
    // 下一页
    if (data.current < data.totalPages) {
        html += `<button onclick="changePage(${data.current + 1})">下一页</button>`;
    }
    
    // 页面信息
    html += `<span style="margin-left: 20px;">第 ${data.current} 页，共 ${data.totalPages} 页，总计 ${data.total} 条</span>`;
    
    pagination.innerHTML = html;
}

/**
 * 切换页面
 */
function changePage(page) {
    currentPage = page;
    loadAccessLogs();
}

/**
 * 查看日志详情
 */
async function viewLogDetail(logId) {
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/detail/${logId}`);
        const result = await response.json();
        
        if (result.code === 200) {
            showLogDetailModal(result.data);
        } else {
            showAlert('logs-alert', result.message || '获取详情失败');
        }
    } catch (error) {
        console.error('获取日志详情失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示日志详情模态框
 */
function showLogDetailModal(data) {
    const modal = document.getElementById('detailModal');
    const content = document.getElementById('detailContent');

    const html = `
        <div class="detail-grid">
            <div class="detail-item">
                <label>追踪ID:</label>
                <span>${data.traceId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>用户ID:</label>
                <span>${data.userId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>用户名:</label>
                <span>${data.userName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>真实姓名:</label>
                <span>${data.realName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>会话ID:</label>
                <span>${data.sessionId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>请求方法:</label>
                <span>${data.requestMethod || '-'}</span>
            </div>
            <div class="detail-item">
                <label>HTTP状态码:</label>
                <span>${data.httpStatus || '-'}</span>
            </div>
            <div class="detail-item">
                <label>响应大小:</label>
                <span>${data.responseSize ? formatBytes(data.responseSize) : '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>请求URL:</label>
                <span>${data.requestUrl || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>方法名:</label>
                <span>${data.methodName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>开始时间:</label>
                <span>${data.requestStartTimeFormatted || (data.requestStartTime ? new Date(data.requestStartTime).toLocaleString() : '-')}</span>
            </div>
            <div class="detail-item">
                <label>结束时间:</label>
                <span>${data.requestEndTimeFormatted || (data.requestEndTime ? new Date(data.requestEndTime).toLocaleString() : '-')}</span>
            </div>
            <div class="detail-item">
                <label>响应时间:</label>
                <span>${data.responseTime ? data.responseTime + 'ms' : '-'}</span>
            </div>
            <div class="detail-item">
                <label>请求持续时间:</label>
                <span>${data.requestDuration ? data.requestDuration + 'ms' : '-'}</span>
            </div>
            <div class="detail-item">
                <label>执行状态:</label>
                <span class="${data.isSuccess ? 'status-success' : 'status-error'}">${data.isSuccess ? '成功' : '失败'}</span>
            </div>
            <div class="detail-item">
                <label>IP地址:</label>
                <span>${data.requestIp || '-'}</span>
            </div>
            <div class="detail-item">
                <label>地理位置:</label>
                <span>${data.location || '-'}</span>
            </div>
            <div class="detail-item">
                <label>浏览器:</label>
                <span>${data.browser || '-'}</span>
            </div>
            <div class="detail-item">
                <label>操作系统:</label>
                <span>${data.operatingSystem || '-'}</span>
            </div>
            <div class="detail-item">
                <label>设备类型:</label>
                <span>${data.deviceType || '-'}</span>
            </div>
            <div class="detail-item">
                <label>业务类型:</label>
                <span>${data.businessType || '-'}</span>
            </div>
            <div class="detail-item">
                <label>操作类型:</label>
                <span>${data.operatorType || '-'}</span>
            </div>
            <div class="detail-item">
                <label>数据来源:</label>
                <span>${data.dataFrom || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>User-Agent:</label>
                <span style="word-break: break-all; font-family: 'Courier New', monospace; font-size: 12px;">${data.userAgent || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>Referer:</label>
                <span style="word-break: break-all; font-family: 'Courier New', monospace; font-size: 12px;">${data.referer || '-'}</span>
            </div>
            ${data.requestParam && data.requestParam !== '-' ? `
            <div class="detail-item full-width">
                <label>请求参数:</label>
                ${formatJsonField(data.requestParam, 'requestParam')}
            </div>
            ` : ''}
            ${data.responseResult && data.responseResult !== '-' ? `
            <div class="detail-item full-width">
                <label>响应结果:</label>
                ${formatJsonField(data.responseResult, 'responseResult')}
            </div>
            ` : ''}
            ${data.errorMessage ? `
            <div class="detail-item full-width">
                <label>错误信息:</label>
                <div class="error-text">${data.errorMessage}</div>
            </div>
            ` : ''}
            ${data.exceptionType ? `
            <div class="detail-item full-width">
                <label>异常类型:</label>
                <span style="color: #dc3545; font-family: 'Courier New', monospace;">${data.exceptionType}</span>
            </div>
            ` : ''}
            ${data.exceptionStack ? `
            <div class="detail-item full-width">
                <label>异常堆栈:</label>
                <div class="json-container">
                    <button class="copy-btn" onclick="copyToClipboard('exceptionStack', this)">复制</button>
                    <div class="json-content" id="exceptionStack">${data.exceptionStack}</div>
                </div>
            </div>
            ` : ''}
        </div>
    `;

    content.innerHTML = html;
    modal.style.display = 'block';
}

/**
 * 格式化JSON字段
 */
function formatJsonField(jsonStr, fieldId) {
    if (!jsonStr || jsonStr === '-') {
        return '<span>-</span>';
    }

    try {
        // 尝试解析JSON
        const parsed = JSON.parse(jsonStr);
        const formatted = JSON.stringify(parsed, null, 2);
        return `
            <div class="json-container">
                <button class="copy-btn" onclick="copyToClipboard('${fieldId}', this)">复制</button>
                <div class="json-content" id="${fieldId}">${escapeHtml(formatted)}</div>
            </div>
        `;
    } catch (e) {
        // 如果不是JSON，直接显示
        return `
            <div class="json-container">
                <button class="copy-btn" onclick="copyToClipboard('${fieldId}', this)">复制</button>
                <div class="json-content" id="${fieldId}">${escapeHtml(jsonStr)}</div>
            </div>
        `;
    }
}

/**
 * 转义HTML字符
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 格式化字节大小
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(elementId, button) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const text = element.textContent;

    // 使用现代API
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccess(button);
        }).catch(() => {
            fallbackCopyTextToClipboard(text, button);
        });
    } else {
        fallbackCopyTextToClipboard(text, button);
    }
}

/**
 * 备用复制方法
 */
function fallbackCopyTextToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess(button);
    } catch (err) {
        console.error('复制失败:', err);
    }

    document.body.removeChild(textArea);
}

/**
 * 显示复制成功
 */
function showCopySuccess(button) {
    const originalText = button.textContent;
    button.textContent = '已复制';
    button.classList.add('copied');

    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('copied');
    }, 2000);
}

/**
 * 关闭详情模态框
 */
function closeDetailModal() {
    document.getElementById('detailModal').style.display = 'none';
}

/**
 * 搜索日志
 */
function searchLogs() {
    currentPage = 1;
    loadAccessLogs();
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('userId').value = '';
    document.getElementById('userName').value = '';
    document.getElementById('requestUrl').value = '';
    document.getElementById('requestMethod').value = '';
    document.getElementById('status').value = '';
    document.getElementById('startTime').value = '';
    document.getElementById('endTime').value = '';
    document.getElementById('requestIp').value = '';
    
    currentPage = 1;
    loadAccessLogs();
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        showLoading('statisticsContent');
        
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`);
        const result = await response.json();
        
        if (result.code === 200) {
            displayStatistics(result.data);
        } else {
            showAlert('statistics-alert', result.message || '加载统计数据失败');
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showAlert('statistics-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示统计数据
 */
function displayStatistics(data) {
    const container = document.getElementById('statisticsContent');
    
    const html = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div class="detail-item">
                <label>总请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #667eea;">${data.totalRequests || 0}</span>
            </div>
            <div class="detail-item">
                <label>成功请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #28a745;">${data.successRequests || 0}</span>
            </div>
            <div class="detail-item">
                <label>失败请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #dc3545;">${data.errorRequests || 0}</span>
            </div>
            <div class="detail-item">
                <label>成功率:</label>
                <span style="font-size: 24px; font-weight: bold; color: #28a745;">${data.successRate || '0%'}</span>
            </div>
            <div class="detail-item">
                <label>平均响应时间:</label>
                <span style="font-size: 24px; font-weight: bold; color: #667eea;">${data.avgResponseTime || 0}ms</span>
            </div>
            <div class="detail-item">
                <label>慢请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #ffc107;">${data.slowRequests || 0}</span>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// 监控相关变量
let monitoringTimer = null;
let isMonitoringActive = false;
let forceRefreshTimer = null;
let refreshCount = 0;

/**
 * 加载监控数据
 */
async function loadMonitoring() {
    try {
        showLoading('monitoringContent');

        // 同时加载监控数据和统计数据
        const [monitoringResponse, statisticsResponse] = await Promise.all([
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring`),
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`)
        ]);

        const monitoringResult = await monitoringResponse.json();
        const statisticsResult = await statisticsResponse.json();

        if (monitoringResult.code === 200 && statisticsResult.code === 200) {
            // 检查统计指标值是否存在问题
            const dataIssues = validateStatisticsData(statisticsResult.data);
            if (dataIssues.length > 0) {
                console.warn('发现统计数据问题:', dataIssues);
                showDataIssuesNotification(dataIssues);
            }

            displayMonitoring(monitoringResult.data, statisticsResult.data);
            // 启动实时监控
            startRealTimeMonitoring();
        } else {
            showAlert('monitoring-alert', '加载监控数据失败');
        }
    } catch (error) {
        console.error('加载监控数据失败:', error);
        showAlert('monitoring-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示监控数据
 */
function displayMonitoring(monitoringData, statisticsData) {
    const container = document.getElementById('monitoringContent');

    // 获取实时数据
    const realTime = statisticsData.realTime || {};
    const systemHealthy = monitoringData.systemHealthy !== false;
    const healthMessage = monitoringData.healthMessage || '系统运行正常';

    const html = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <div>
                <h3 style="margin: 0;">实时监控</h3>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    <span id="refreshStatus">监控状态</span>
                </div>
            </div>
            <div>
                <button class="refresh-btn" onclick="refreshMonitoring()">
                    <span id="refreshIcon">🔄</span> 刷新数据
                </button>
                <button class="refresh-btn" onclick="forceRefreshMonitoring()" style="background: #dc3545;">
                    ⚡ 强制刷新
                </button>
                <button class="refresh-btn" onclick="executeJobTask()" style="background: #28a745;">
                    <span id="jobTaskIcon">⚙️</span> 任务刷新
                </button>
                <button class="refresh-btn" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                    ${isMonitoringActive ? '停止自动刷新' : '开启自动刷新'}
                </button>
            </div>
        </div>

        <!-- 系统健康状态 -->
        <div class="health-indicator ${systemHealthy ? 'healthy' : 'unhealthy'}">
            <span style="font-size: 20px;">${systemHealthy ? '✅' : '⚠️'}</span>
            <div>
                <strong>系统状态: ${systemHealthy ? '健康' : '异常'}</strong>
                <div style="font-size: 14px; margin-top: 5px;">${healthMessage}</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    更新时间: ${monitoringData.monitoringTime || new Date().toLocaleString()}
                </div>
            </div>
        </div>

        <!-- 实时指标 -->
        <div class="monitoring-grid">
            <div class="monitoring-card">
                <h4>📊 实时请求统计</h4>
                <div class="metric-value primary">${realTime.totalRequests || 0}</div>
                <div style="font-size: 14px; color: #666;">总请求数 (最近1小时)</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px;">成功: <span class="metric-value success" style="font-size: 16px;">${realTime.successRequests || 0}</span></div>
                    <div style="font-size: 14px;">失败: <span class="metric-value danger" style="font-size: 16px;">${realTime.errorRequests || 0}</span></div>
                </div>
            </div>

            <div class="monitoring-card">
                <h4>⚡ 性能指标</h4>
                <div class="metric-value ${getPerformanceClass(realTime.avgResponseTime)}">${realTime.avgResponseTime || 0}ms</div>
                <div style="font-size: 14px; color: #666;">平均响应时间</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px;">最大响应时间: <span style="font-weight: bold;">${realTime.maxResponseTime || 0}ms</span></div>
                </div>
            </div>

            <div class="monitoring-card">
                <h4>📈 成功率</h4>
                <div class="metric-value ${getSuccessRateClass(realTime.successRate)}">${realTime.successRate || 0}%</div>
                <div style="font-size: 14px; color: #666;">请求成功率</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px;">错误率: <span class="metric-value danger" style="font-size: 16px;">${realTime.errorRate || 0}%</span></div>
                </div>
            </div>

            <div class="monitoring-card">
                <h4>👥 活跃用户</h4>
                <div class="metric-value primary">${realTime.activeUsers || 0}</div>
                <div style="font-size: 14px; color: #666;">在线用户数</div>
                <div style="margin-top: 10px;">
                    <div style="font-size: 14px;">独立IP: <span style="font-weight: bold;">${realTime.uniqueIps || 0}</span></div>
                </div>
            </div>
        </div>

        <!-- 热点数据 -->
        <div class="monitoring-grid">
            <div class="monitoring-card">
                <h4>🔥 热点URL (最近1小时)</h4>
                <div id="hotUrls">
                    ${formatHotUrls(statisticsData.hotUrls || [])}
                </div>
            </div>

            <div class="monitoring-card">
                <h4>🚨 最近错误</h4>
                <div id="recentErrors">
                    ${formatRecentErrors(statisticsData.recentErrors || [])}
                </div>
            </div>

            <div class="monitoring-card">
                <h4>🐌 慢请求监控</h4>
                <div id="slowRequests">
                    ${formatSlowRequests(statisticsData.slowRequests || [])}
                </div>
            </div>

            <div class="monitoring-card">
                <h4>🌍 访问来源</h4>
                <div id="topIps">
                    ${formatTopIps(statisticsData.topIps || [])}
                </div>
            </div>
        </div>

        <!-- 趋势图表区域 -->
        <div class="monitoring-card" style="margin-top: 20px;">
            <h4>📊 24小时访问趋势</h4>
            <div id="trendChart">
                ${formatTrendData(statisticsData.hourlyTrend || [])}
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * 获取性能等级样式类
 */
function getPerformanceClass(responseTime) {
    if (!responseTime) return 'primary';
    if (responseTime < 200) return 'success';
    if (responseTime < 1000) return 'warning';
    return 'danger';
}

/**
 * 获取成功率样式类
 */
function getSuccessRateClass(successRate) {
    if (!successRate) return 'danger';
    if (successRate >= 95) return 'success';
    if (successRate >= 90) return 'warning';
    return 'danger';
}

/**
 * 格式化热点URL
 */
function formatHotUrls(hotUrls) {
    if (!hotUrls || hotUrls.length === 0) {
        return '<div style="color: #666; font-size: 14px;">暂无数据</div>';
    }

    return hotUrls.slice(0, 5).map((item, index) => `
        <div style="display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee;">
            <span style="font-size: 14px; color: #333;">${index + 1}. ${truncateText(item.url || item.requestUrl || '-', 30)}</span>
            <span style="font-weight: bold; color: #667eea;">${item.count || item.accessCount || 0}</span>
        </div>
    `).join('');
}

/**
 * 格式化最近错误
 */
function formatRecentErrors(recentErrors) {
    if (!recentErrors || recentErrors.length === 0) {
        return '<div style="color: #28a745; font-size: 14px;">✅ 暂无错误</div>';
    }

    return recentErrors.slice(0, 3).map(error => `
        <div style="padding: 8px; margin: 5px 0; background: #f8d7da; border-radius: 5px; border-left: 3px solid #dc3545;">
            <div style="font-size: 12px; color: #721c24; font-weight: bold;">
                ${error.requestMethod || '-'} ${truncateText(error.requestUrl || '-', 25)}
            </div>
            <div style="font-size: 11px; color: #856404; margin-top: 3px;">
                ${error.requestStartTime ? new Date(error.requestStartTime).toLocaleTimeString() : '-'}
            </div>
            ${error.errorMessage ? `
            <div style="font-size: 11px; color: #721c24; margin-top: 3px;">
                ${truncateText(error.errorMessage, 40)}
            </div>
            ` : ''}
        </div>
    `).join('');
}

/**
 * 格式化慢请求
 */
function formatSlowRequests(slowRequests) {
    if (!slowRequests || slowRequests.length === 0) {
        return '<div style="color: #28a745; font-size: 14px;">✅ 暂无慢请求</div>';
    }

    return slowRequests.slice(0, 3).map(slow => `
        <div style="padding: 8px; margin: 5px 0; background: #fff3cd; border-radius: 5px; border-left: 3px solid #ffc107;">
            <div style="font-size: 12px; color: #856404; font-weight: bold;">
                ${slow.requestMethod || '-'} ${truncateText(slow.requestUrl || '-', 25)}
            </div>
            <div style="font-size: 11px; color: #856404; margin-top: 3px;">
                响应时间: <span style="font-weight: bold;">${slow.responseTime || 0}ms</span>
            </div>
            <div style="font-size: 11px; color: #856404; margin-top: 3px;">
                ${slow.requestStartTime ? new Date(slow.requestStartTime).toLocaleTimeString() : '-'}
            </div>
        </div>
    `).join('');
}

/**
 * 格式化访问来源IP
 */
function formatTopIps(topIps) {
    if (!topIps || topIps.length === 0) {
        return '<div style="color: #666; font-size: 14px;">暂无数据</div>';
    }

    return topIps.slice(0, 5).map((item, index) => `
        <div style="display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee;">
            <span style="font-size: 14px; color: #333;">${index + 1}. ${item.ip || item.requestIp || '-'}</span>
            <span style="font-weight: bold; color: #667eea;">${item.count || item.accessCount || 0}</span>
        </div>
    `).join('');
}

/**
 * 格式化趋势数据
 */
function formatTrendData(hourlyTrend) {
    if (!hourlyTrend || hourlyTrend.length === 0) {
        return '<div style="color: #666; font-size: 14px; text-align: center; padding: 20px;">暂无趋势数据</div>';
    }

    // 简单的文本图表显示
    const maxCount = Math.max(...hourlyTrend.map(item => item.count || item.accessCount || 0));

    return `
        <div style="font-family: 'Courier New', monospace; font-size: 12px; background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
            ${hourlyTrend.map(item => {
                const count = item.count || item.accessCount || 0;
                const percentage = maxCount > 0 ? (count / maxCount * 100) : 0;
                const barLength = Math.max(1, Math.floor(percentage / 2));
                const bar = '█'.repeat(barLength);
                const time = item.hour || item.time || '-';

                return `<div style="margin: 2px 0;">${time.toString().padEnd(8)} │${bar} ${count}</div>`;
            }).join('')}
        </div>
    `;
}

/**
 * 启动实时监控
 */
function startRealTimeMonitoring() {
    if (monitoringTimer) {
        clearInterval(monitoringTimer);
    }
    if (forceRefreshTimer) {
        clearInterval(forceRefreshTimer);
    }

    isMonitoringActive = true;
    refreshCount = 0;
    updateAutoRefreshButton();

    // 每30秒刷新一次监控数据
    monitoringTimer = setInterval(() => {
        refreshMonitoring();
    }, 30000);

    // 强制刷新机制：每10秒检查一次，如果超过2分钟没有刷新则强制刷新
    forceRefreshTimer = setInterval(() => {
        refreshCount++;

        // 每10秒更新一次刷新计数显示
        updateRefreshStatus();

        // 如果超过2分钟（12个10秒周期）没有正常刷新，强制刷新
        if (refreshCount >= 12) {
            console.warn('检测到监控数据可能停止更新，执行强制刷新');
            forceRefreshMonitoring();
            refreshCount = 0;
        }
    }, 10000);

    console.log('实时监控已启动，每30秒自动刷新，每10秒检查状态');
}

/**
 * 停止实时监控
 */
function stopRealTimeMonitoring() {
    if (monitoringTimer) {
        clearInterval(monitoringTimer);
        monitoringTimer = null;
    }
    if (forceRefreshTimer) {
        clearInterval(forceRefreshTimer);
        forceRefreshTimer = null;
    }

    isMonitoringActive = false;
    refreshCount = 0;
    updateAutoRefreshButton();
    updateRefreshStatus();

    console.log('实时监控已停止');
}

/**
 * 切换自动刷新
 */
function toggleAutoRefresh() {
    if (isMonitoringActive) {
        stopRealTimeMonitoring();
    } else {
        startRealTimeMonitoring();
    }
}

/**
 * 更新自动刷新按钮状态
 */
function updateAutoRefreshButton() {
    const button = document.getElementById('autoRefreshBtn');
    if (button) {
        button.textContent = isMonitoringActive ? '停止自动刷新' : '开启自动刷新';
        button.style.background = isMonitoringActive ? '#dc3545' : '#17a2b8';
    }
}

/**
 * 手动刷新监控数据
 */
async function refreshMonitoring() {
    const refreshIcon = document.getElementById('refreshIcon');
    if (refreshIcon) {
        refreshIcon.style.animation = 'spin 1s linear infinite';
    }

    try {
        // 重新加载监控数据
        const [monitoringResponse, statisticsResponse] = await Promise.all([
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring`),
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`)
        ]);

        const monitoringResult = await monitoringResponse.json();
        const statisticsResult = await statisticsResponse.json();

        if (monitoringResult.code === 200 && statisticsResult.code === 200) {
            // 检查统计指标值是否存在问题
            const dataIssues = validateStatisticsData(statisticsResult.data);
            if (dataIssues.length > 0) {
                console.warn('刷新时发现统计数据问题:', dataIssues);
            }

            displayMonitoring(monitoringResult.data, statisticsResult.data);
            refreshCount = 0; // 重置刷新计数
            updateRefreshStatus();
            console.log('监控数据刷新成功:', new Date().toLocaleTimeString());
        } else {
            console.error('监控数据刷新失败');
        }
    } catch (error) {
        console.error('刷新监控数据失败:', error);
    } finally {
        if (refreshIcon) {
            refreshIcon.style.animation = '';
        }
    }
}

/**
 * 强制刷新监控数据
 */
async function forceRefreshMonitoring() {
    console.log('执行强制刷新监控数据');

    // 显示强制刷新状态
    const refreshIcon = document.getElementById('refreshIcon');
    if (refreshIcon) {
        refreshIcon.style.animation = 'spin 2s linear infinite';
        refreshIcon.style.color = '#dc3545'; // 红色表示强制刷新
    }

    try {
        // 添加时间戳参数强制刷新
        const timestamp = Date.now();
        const [monitoringResponse, statisticsResponse] = await Promise.all([
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring?_t=${timestamp}`),
            makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics?_t=${timestamp}`)
        ]);

        const monitoringResult = await monitoringResponse.json();
        const statisticsResult = await statisticsResponse.json();

        if (monitoringResult.code === 200 && statisticsResult.code === 200) {
            // 检查统计指标值是否存在问题
            const dataIssues = validateStatisticsData(statisticsResult.data);
            if (dataIssues.length > 0) {
                console.warn('强制刷新时发现统计数据问题:', dataIssues);
                showDataIssuesNotification(dataIssues);
            }

            displayMonitoring(monitoringResult.data, statisticsResult.data);
            refreshCount = 0; // 重置刷新计数
            updateRefreshStatus();
            console.log('强制刷新监控数据成功:', new Date().toLocaleTimeString());

            // 显示强制刷新成功提示
            showForceRefreshNotification('强制刷新成功');
        } else {
            console.error('强制刷新监控数据失败');
            showForceRefreshNotification('强制刷新失败', 'error');
        }
    } catch (error) {
        console.error('强制刷新监控数据失败:', error);
        showForceRefreshNotification('强制刷新失败: ' + error.message, 'error');
    } finally {
        if (refreshIcon) {
            refreshIcon.style.animation = '';
            refreshIcon.style.color = ''; // 恢复原色
        }
    }
}

/**
 * 更新刷新状态显示
 */
function updateRefreshStatus() {
    const statusElement = document.getElementById('refreshStatus');
    if (statusElement) {
        if (isMonitoringActive) {
            const nextRefresh = 30 - (refreshCount * 10) % 30;
            statusElement.textContent = `下次刷新: ${nextRefresh}秒`;
            statusElement.style.color = refreshCount >= 10 ? '#dc3545' : '#28a745';
        } else {
            statusElement.textContent = '监控已停止';
            statusElement.style.color = '#6c757d';
        }
    }
}

/**
 * 显示强制刷新通知
 */
function showForceRefreshNotification(message, type = 'success') {
    const notification = document.createElement('div');

    let backgroundColor, textColor;
    switch (type) {
        case 'error':
            backgroundColor = '#dc3545';
            textColor = 'white';
            break;
        case 'warning':
            backgroundColor = '#ffc107';
            textColor = '#212529';
            break;
        case 'success':
        default:
            backgroundColor = '#28a745';
            textColor = 'white';
            break;
    }

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 10px 20px;
        border-radius: 5px;
        color: ${textColor};
        font-weight: bold;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
        background: ${backgroundColor};
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * 执行定时任务刷新
 */
async function executeJobTask() {
    console.log('开始执行定时任务刷新');

    const jobTaskIcon = document.getElementById('jobTaskIcon');
    if (jobTaskIcon) {
        jobTaskIcon.style.animation = 'spin 2s linear infinite';
    }

    try {
        // 首先获取可用的定时任务列表
        const jobList = await getAvailableJobs();

        if (!jobList || jobList.length === 0) {
            showForceRefreshNotification('未找到可执行的定时任务', 'error');
            return;
        }

        // 查找访问日志相关的任务
        const accessLogJobs = jobList.filter(job =>
            job.jobName && (
                job.jobName.toLowerCase().includes('access') ||
                job.jobName.toLowerCase().includes('log') ||
                job.jobName.toLowerCase().includes('record') ||
                job.jobName.toLowerCase().includes('statistics') ||
                job.jobName.toLowerCase().includes('clean') ||
                job.jobName.toLowerCase().includes('optimize')
            )
        );

        if (accessLogJobs.length === 0) {
            showForceRefreshNotification('未找到访问日志相关的定时任务', 'error');
            return;
        }

        // 执行找到的任务
        let successCount = 0;
        let totalCount = accessLogJobs.length;

        for (const job of accessLogJobs) {
            try {
                const result = await executeJob(job);
                if (result) {
                    successCount++;
                    console.log(`任务执行成功: ${job.jobName}`);
                } else {
                    console.error(`任务执行失败: ${job.jobName}`);
                }
            } catch (error) {
                console.error(`任务执行异常: ${job.jobName}`, error);
            }
        }

        // 显示执行结果
        if (successCount === totalCount) {
            showForceRefreshNotification(`任务刷新成功 (${successCount}/${totalCount})`, 'success');
        } else if (successCount > 0) {
            showForceRefreshNotification(`部分任务执行成功 (${successCount}/${totalCount})`, 'warning');
        } else {
            showForceRefreshNotification(`任务执行失败 (${successCount}/${totalCount})`, 'error');
        }

        // 等待一段时间后刷新监控数据
        setTimeout(() => {
            refreshMonitoring();
        }, 2000);

    } catch (error) {
        console.error('执行定时任务失败:', error);
        showForceRefreshNotification('任务执行失败: ' + error.message, 'error');
    } finally {
        if (jobTaskIcon) {
            jobTaskIcon.style.animation = '';
        }
    }
}

/**
 * 获取可用的定时任务列表
 */
async function getAvailableJobs() {
    try {
        const response = await fetch(`${getApiBaseUrl()}/quartz/job/list`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            return result.data || [];
        } else {
            throw new Error(result.message || '获取任务列表失败');
        }
    } catch (error) {
        console.error('获取定时任务列表失败:', error);
        throw error;
    }
}

/**
 * 执行指定的定时任务
 */
async function executeJob(job) {
    try {
        const response = await fetch(`${getApiBaseUrl()}/quartz/job/run`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                jobId: job.jobId,
                jobGroup: job.jobGroup || 'DEFAULT'
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            return true;
        } else {
            console.error(`任务执行失败: ${job.jobName}`, result.message);
            return false;
        }
    } catch (error) {
        console.error(`执行任务异常: ${job.jobName}`, error);
        return false;
    }
}

/**
 * 验证统计数据是否存在问题
 */
function validateStatisticsData(statisticsData) {
    const issues = [];

    if (!statisticsData) {
        issues.push('统计数据为空');
        return issues;
    }

    // 检查基础统计数据
    const basicStats = statisticsData.basicStats || {};

    // 检查总记录数是否合理
    const totalRecords = basicStats.totalRecords || 0;
    if (totalRecords < 0) {
        issues.push('总记录数为负数');
    }

    // 检查成功率是否合理
    const successRate = basicStats.successRate || 0;
    if (successRate < 0 || successRate > 100) {
        issues.push(`成功率异常: ${successRate}%`);
    }

    // 检查错误率是否过高
    const errorRate = basicStats.errorRate || 0;
    if (errorRate > 50) {
        issues.push(`错误率过高: ${errorRate}%`);
    }

    // 检查平均响应时间是否异常
    const avgResponseTime = basicStats.avgResponseTime || 0;
    if (avgResponseTime < 0) {
        issues.push('平均响应时间为负数');
    } else if (avgResponseTime > 10000) {
        issues.push(`平均响应时间过长: ${avgResponseTime}ms`);
    }

    // 检查实时数据
    const realTime = statisticsData.realTime || {};
    const realtimeTotal = realTime.totalRequests || 0;
    const realtimeSuccess = realTime.successRequests || 0;
    const realtimeError = realTime.errorRequests || 0;

    // 检查实时数据一致性
    if (realtimeTotal !== (realtimeSuccess + realtimeError)) {
        issues.push('实时统计数据不一致');
    }

    // 检查实时成功率
    const realtimeSuccessRate = realTime.successRate || 0;
    if (realtimeTotal > 0) {
        const calculatedRate = (realtimeSuccess / realtimeTotal) * 100;
        if (Math.abs(calculatedRate - realtimeSuccessRate) > 1) {
            issues.push('实时成功率计算错误');
        }
    }

    // 检查热点数据
    const hotUrls = statisticsData.hotUrls || [];
    if (Array.isArray(hotUrls)) {
        hotUrls.forEach((item, index) => {
            if (!item.url && !item.requestUrl) {
                issues.push(`热点URL第${index + 1}项缺少URL字段`);
            }
            if (!item.count && !item.accessCount) {
                issues.push(`热点URL第${index + 1}项缺少计数字段`);
            }
        });
    }

    // 检查趋势数据
    const hourlyTrend = statisticsData.hourlyTrend || [];
    if (Array.isArray(hourlyTrend)) {
        hourlyTrend.forEach((item, index) => {
            const count = item.count || item.accessCount || 0;
            if (count < 0) {
                issues.push(`趋势数据第${index + 1}项计数为负数`);
            }
        });
    }

    // 检查IP统计
    const topIps = statisticsData.topIps || [];
    if (Array.isArray(topIps)) {
        topIps.forEach((item, index) => {
            if (!item.ip && !item.requestIp) {
                issues.push(`IP统计第${index + 1}项缺少IP字段`);
            }
        });
    }

    return issues;
}

/**
 * 显示数据问题通知
 */
function showDataIssuesNotification(issues) {
    if (!issues || issues.length === 0) return;

    const message = `发现${issues.length}个数据问题: ${issues.slice(0, 3).join(', ')}${issues.length > 3 ? '...' : ''}`;
    showForceRefreshNotification(message, 'warning');

    // 在控制台输出详细信息
    console.group('📊 统计数据问题详情');
    issues.forEach((issue, index) => {
        console.warn(`${index + 1}. ${issue}`);
    });
    console.groupEnd();
}

/**
 * 启动Token状态监控
 */
function startTokenStatusMonitor() {
    // 获取Token过期时间
    const expireTimeStr = sessionStorage.getItem('accessLogTokenExpire');
    if (expireTimeStr) {
        tokenExpireTime = parseInt(expireTimeStr);
    }

    // 更新状态显示
    updateTokenStatus();

    // 启动定时器，每秒更新一次
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
    }

    tokenStatusTimer = setInterval(() => {
        updateTokenStatus();
    }, 1000);
}

/**
 * 更新Token状态显示
 */
function updateTokenStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const expireTime = document.getElementById('expireTime');

    if (!tokenExpireTime) {
        statusText.textContent = '已认证';
        expireTime.textContent = '永久有效';
        statusDot.className = 'status-dot';
        return;
    }

    const now = Date.now();
    const remainingTime = tokenExpireTime - now;

    if (remainingTime <= 0) {
        // Token已过期
        statusText.textContent = '已过期';
        expireTime.textContent = '00:00:00';
        statusDot.className = 'status-dot danger';

        // 自动跳转到登录页面
        setTimeout(() => {
            logout();
        }, 2000);
        return;
    }

    // 计算剩余时间
    const hours = Math.floor(remainingTime / (1000 * 60 * 60));
    const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    expireTime.textContent = timeStr;

    // 根据剩余时间设置状态
    if (remainingTime < 5 * 60 * 1000) { // 少于5分钟
        statusText.textContent = '即将过期';
        statusDot.className = 'status-dot danger';
    } else if (remainingTime < 15 * 60 * 1000) { // 少于15分钟
        statusText.textContent = '即将过期';
        statusDot.className = 'status-dot warning';
    } else {
        statusText.textContent = '已认证';
        statusDot.className = 'status-dot';
    }
}

/**
 * 退出登录
 */
function logout() {
    // 清除Token状态监控
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
        tokenStatusTimer = null;
    }

    // 清除实时监控
    if (monitoringTimer) {
        clearInterval(monitoringTimer);
        monitoringTimer = null;
    }
    if (forceRefreshTimer) {
        clearInterval(forceRefreshTimer);
        forceRefreshTimer = null;
    }
    isMonitoringActive = false;
    refreshCount = 0;

    // 清除存储的Token信息
    sessionStorage.removeItem('accessLogToken');
    sessionStorage.removeItem('accessLogTokenExpire');

    // 重置全局变量
    accessToken = '';
    isAuthenticated = false;
    tokenExpireTime = 0;

    // 隐藏主要内容和Token状态
    document.getElementById('mainContent').style.display = 'none';
    document.getElementById('tokenStatus').style.display = 'none';

    // 显示认证区域
    document.getElementById('authSection').style.display = 'block';
    document.getElementById('secretKeyForm').style.display = 'block';
    document.getElementById('tokenForm').style.display = 'none';
    document.getElementById('validateForm').style.display = 'none';

    // 清空表单
    document.getElementById('secretKey').value = '';
    document.getElementById('code').value = '';
    document.getElementById('refreshCode').value = '';

    // 清空提示信息
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.style.display = 'none';
        alert.textContent = '';
    });

    console.log('已退出登录');
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('detailModal');
    if (event.target === modal) {
        closeDetailModal();
    }
}
