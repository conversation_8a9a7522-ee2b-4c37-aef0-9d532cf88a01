/**
 * 系统访问日志管理 JavaScript
 * 提供完整的日志查询、统计和监控功能
 */

// 全局变量
let currentPage = 1;
let currentPageSize = 20;
let accessToken = '';
let isAuthenticated = false;
let tokenExpireTime = 0;
let tokenStatusTimer = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('系统访问日志管理页面初始化...');
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 检查是否已经认证
    const savedToken = sessionStorage.getItem('accessLogToken');
    const tokenExpire = sessionStorage.getItem('accessLogTokenExpire');

    if (savedToken && tokenExpire && Date.now() < parseInt(tokenExpire)) {
        accessToken = savedToken;
        tokenExpireTime = parseInt(tokenExpire);
        isAuthenticated = true;
        showMainContent();
        // 默认加载访问日志数据
        loadAccessLogs();
    }

    // 绑定回车键事件
    document.getElementById('secretKey').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifySecretKey();
        }
    });

    const codeInput = document.getElementById('code');
    const refreshCodeInput = document.getElementById('refreshCode');

    if (codeInput) {
        codeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }

    if (refreshCodeInput) {
        refreshCodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }
}

/**
 * 获取API基础URL
 */
function getApiBaseUrl() {
    if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
        return window.SERVER_CONFIG.requestUrl + '/api';
    }
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}/api`;
}

/**
 * 显示提示信息
 */
function showAlert(containerId, message, type = 'danger') {
    const alertContainer = document.getElementById(containerId);
    if (alertContainer) {
        alertContainer.className = `alert ${type}`;
        alertContainer.textContent = message;
        alertContainer.style.display = 'block';
        
        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                alertContainer.style.display = 'none';
            }, 3000);
        }
    }
}

/**
 * 验证秘钥
 */
async function verifySecretKey() {
    const secretKey = document.getElementById('secretKey').value.trim();
    
    if (!secretKey) {
        showAlert('secretAlert', '请输入秘钥');
        return;
    }
    
    try {
        const response = await fetch(`${getApiBaseUrl()}/access-log/auth/verify-secret`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ secretKey })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('secretAlert', '秘钥验证成功，请获取访问令牌', 'success');
            document.getElementById('secretKeyForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        } else {
            showAlert('secretAlert', result.message || '秘钥验证失败');
        }
    } catch (error) {
        console.error('秘钥验证失败:', error);
        showAlert('secretAlert', '网络错误，请稍后重试');
    }
}

/**
 * 获取访问令牌
 */
async function getAccessToken() {
    const code = document.getElementById('code').value.trim();
    const refreshCode = document.getElementById('refreshCode').value.trim();

    if (!code || !refreshCode) {
        showAlert('tokenAlert', '请输入验证码和刷新码');
        return;
    }

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/getAccessToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: code,
                refreshCode: refreshCode
            })
        });

        const result = await response.json();

        if (result.code === 200 && result.data && result.data.accessToken) {
            accessToken = result.data.accessToken;
            console.log('AccessToken获取成功:', accessToken);

            // 保存token到sessionStorage
            sessionStorage.setItem('accessLogToken', accessToken);
            if (result.data.expiresIn) {
                tokenExpireTime = Date.now() + (result.data.expiresIn * 1000);
                sessionStorage.setItem('accessLogTokenExpire', tokenExpireTime);
            }

            showAlert('tokenAlert', '访问令牌获取成功！', 'success');

            // 显示第三步验证界面
            document.getElementById('tokenForm').style.display = 'none';
            document.getElementById('validateForm').style.display = 'block';

            // 自动验证token
            setTimeout(validateAccessToken, 1000);
        } else {
            showAlert('tokenAlert', result.message || '获取访问令牌失败');
        }
    } catch (error) {
        console.error('获取访问令牌失败:', error);
        showAlert('tokenAlert', '网络错误，请稍后重试');
    }
}

/**
 * 验证访问令牌
 */
async function validateAccessToken() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('success-message').style.display = 'none';

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/validate?accessToken=${encodeURIComponent(accessToken)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        document.getElementById('loading').style.display = 'none';

        if (result.code === 200 && result.data && result.data.valid) {
            console.log('Token验证成功，剩余时间:', result.data.remainingTime, '秒');

            // 显示成功消息
            document.getElementById('success-message').style.display = 'block';

            isAuthenticated = true;

            // 延迟跳转到管理页面
            setTimeout(() => {
                showMainContent();
                loadAccessLogs();
            }, 2000);
        } else {
            showAlert('tokenAlert', result.message || 'Token验证失败');
            // 重新显示token获取表单
            document.getElementById('validateForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        }
    } catch (error) {
        console.error('Token验证失败:', error);
        document.getElementById('loading').style.display = 'none';
        showAlert('tokenAlert', '网络错误，请稍后重试');
        // 重新显示token获取表单
        document.getElementById('validateForm').style.display = 'none';
        document.getElementById('tokenForm').style.display = 'block';
    }
}

/**
 * 显示主要内容
 */
function showMainContent() {
    document.getElementById('authSection').style.display = 'none';
    document.getElementById('mainContent').style.display = 'block';
    document.getElementById('tokenStatus').style.display = 'flex';

    // 启动Token状态监控
    startTokenStatusMonitor();
}

/**
 * 切换标签页
 */
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    // 激活当前标签
    event.target.classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // 根据标签页加载对应数据
    switch(tabName) {
        case 'logs':
            loadAccessLogs();
            break;
        case 'statistics':
            loadStatistics();
            break;
        case 'monitoring':
            loadMonitoring();
            break;
    }
}

/**
 * 加载访问日志数据
 */
async function loadAccessLogs() {
    try {
        showLoading('logsContent');
        
        const params = {
            pageNum: currentPage,
            pageSize: currentPageSize,
            userId: document.getElementById('userId').value.trim(),
            userName: document.getElementById('userName').value.trim(),
            requestUrl: document.getElementById('requestUrl').value.trim(),
            requestMethod: document.getElementById('requestMethod').value,
            status: document.getElementById('status').value,
            startTime: document.getElementById('startTime').value,
            endTime: document.getElementById('endTime').value,
            requestIp: document.getElementById('requestIp').value.trim()
        };
        
        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });
        
        const url = `${getApiBaseUrl()}/access-log/management/list`;
        const response = await makeApiRequest(url, {
            method: 'GET',
            data: params
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            displayAccessLogs(result.data);
            showAlert('logs-alert', '数据加载成功', 'success');
        } else {
            showAlert('logs-alert', result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载访问日志失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示加载状态
 */
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }
}

/**
 * 发起API请求
 */
async function makeApiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'X-Access-Token': accessToken
        }
    };
    
    if (options.method === 'GET' && options.data) {
        const params = new URLSearchParams(options.data);
        url += '?' + params.toString();
        delete options.data;
    } else if (options.data) {
        defaultOptions.body = JSON.stringify(options.data);
    }
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    return fetch(url, finalOptions);
}

/**
 * 显示访问日志数据
 */
function displayAccessLogs(data) {
    const container = document.getElementById('logsContent');
    const pagination = document.getElementById('logsPagination');
    
    if (!data || !data.records || data.records.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无数据</div>';
        pagination.style.display = 'none';
        return;
    }
    
    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>用户</th>
                    <th>请求方法</th>
                    <th>请求URL</th>
                    <th>状态</th>
                    <th>响应时间</th>
                    <th>IP地址</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.records.forEach(record => {
        const statusClass = record.isSuccess ? 'status-success' : 'status-error';
        const statusText = record.isSuccess ? '成功' : '失败';
        const responseTime = record.responseTime ? record.responseTime + 'ms' : '-';
        const requestTime = record.requestStartTime ? new Date(record.requestStartTime).toLocaleString() : '-';
        
        html += `
            <tr>
                <td>${requestTime}</td>
                <td>${record.userName || '-'}</td>
                <td>${record.requestMethod || '-'}</td>
                <td title="${record.requestUrl || '-'}">${truncateText(record.requestUrl || '-', 50)}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${responseTime}</td>
                <td>${record.requestIp || '-'}</td>
                <td>
                    <button class="btn-search" onclick="viewLogDetail('${record.id}')">详情</button>
                </td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    container.innerHTML = html;
    
    // 显示分页控件
    if (data.totalPages > 1) {
        displayPagination(data);
        pagination.style.display = 'flex';
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 截断文本
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

/**
 * 显示分页控件
 */
function displayPagination(data) {
    const pagination = document.getElementById('logsPagination');
    let html = '';
    
    // 上一页
    if (data.current > 1) {
        html += `<button onclick="changePage(${data.current - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, data.current - 2);
    const endPage = Math.min(data.totalPages, data.current + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === data.current ? 'active' : '';
        html += `<button class="${activeClass}" onclick="changePage(${i})">${i}</button>`;
    }
    
    // 下一页
    if (data.current < data.totalPages) {
        html += `<button onclick="changePage(${data.current + 1})">下一页</button>`;
    }
    
    // 页面信息
    html += `<span style="margin-left: 20px;">第 ${data.current} 页，共 ${data.totalPages} 页，总计 ${data.total} 条</span>`;
    
    pagination.innerHTML = html;
}

/**
 * 切换页面
 */
function changePage(page) {
    currentPage = page;
    loadAccessLogs();
}

/**
 * 查看日志详情
 */
async function viewLogDetail(logId) {
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/detail/${logId}`);
        const result = await response.json();
        
        if (result.code === 200) {
            showLogDetailModal(result.data);
        } else {
            showAlert('logs-alert', result.message || '获取详情失败');
        }
    } catch (error) {
        console.error('获取日志详情失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示日志详情模态框
 */
function showLogDetailModal(data) {
    const modal = document.getElementById('detailModal');
    const content = document.getElementById('detailContent');
    
    const html = `
        <div class="detail-grid">
            <div class="detail-item">
                <label>追踪ID:</label>
                <span>${data.traceId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>用户ID:</label>
                <span>${data.userId || '-'}</span>
            </div>
            <div class="detail-item">
                <label>用户名:</label>
                <span>${data.userName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>真实姓名:</label>
                <span>${data.realName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>请求方法:</label>
                <span>${data.requestMethod || '-'}</span>
            </div>
            <div class="detail-item">
                <label>HTTP状态码:</label>
                <span>${data.httpStatus || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>请求URL:</label>
                <span>${data.requestUrl || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>方法名:</label>
                <span>${data.methodName || '-'}</span>
            </div>
            <div class="detail-item">
                <label>开始时间:</label>
                <span>${data.requestStartTime ? new Date(data.requestStartTime).toLocaleString() : '-'}</span>
            </div>
            <div class="detail-item">
                <label>结束时间:</label>
                <span>${data.requestEndTime ? new Date(data.requestEndTime).toLocaleString() : '-'}</span>
            </div>
            <div class="detail-item">
                <label>响应时间:</label>
                <span>${data.responseTime ? data.responseTime + 'ms' : '-'}</span>
            </div>
            <div class="detail-item">
                <label>执行状态:</label>
                <span class="${data.isSuccess ? 'status-success' : 'status-error'}">${data.isSuccess ? '成功' : '失败'}</span>
            </div>
            <div class="detail-item">
                <label>IP地址:</label>
                <span>${data.requestIp || '-'}</span>
            </div>
            <div class="detail-item">
                <label>地理位置:</label>
                <span>${data.location || '-'}</span>
            </div>
            <div class="detail-item">
                <label>浏览器:</label>
                <span>${data.browser || '-'}</span>
            </div>
            <div class="detail-item">
                <label>操作系统:</label>
                <span>${data.operatingSystem || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>User-Agent:</label>
                <span style="word-break: break-all;">${data.userAgent || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>请求参数:</label>
                <span style="word-break: break-all;">${data.requestParam || '-'}</span>
            </div>
            <div class="detail-item full-width">
                <label>响应结果:</label>
                <span style="word-break: break-all;">${data.responseResult || '-'}</span>
            </div>
            ${data.errorMessage ? `
            <div class="detail-item full-width">
                <label>错误信息:</label>
                <span style="color: #dc3545; word-break: break-all;">${data.errorMessage}</span>
            </div>
            ` : ''}
        </div>
    `;
    
    content.innerHTML = html;
    modal.style.display = 'block';
}

/**
 * 关闭详情模态框
 */
function closeDetailModal() {
    document.getElementById('detailModal').style.display = 'none';
}

/**
 * 搜索日志
 */
function searchLogs() {
    currentPage = 1;
    loadAccessLogs();
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('userId').value = '';
    document.getElementById('userName').value = '';
    document.getElementById('requestUrl').value = '';
    document.getElementById('requestMethod').value = '';
    document.getElementById('status').value = '';
    document.getElementById('startTime').value = '';
    document.getElementById('endTime').value = '';
    document.getElementById('requestIp').value = '';
    
    currentPage = 1;
    loadAccessLogs();
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        showLoading('statisticsContent');
        
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/statistics`);
        const result = await response.json();
        
        if (result.code === 200) {
            displayStatistics(result.data);
        } else {
            showAlert('statistics-alert', result.message || '加载统计数据失败');
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showAlert('statistics-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示统计数据
 */
function displayStatistics(data) {
    const container = document.getElementById('statisticsContent');
    
    const html = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div class="detail-item">
                <label>总请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #667eea;">${data.totalRequests || 0}</span>
            </div>
            <div class="detail-item">
                <label>成功请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #28a745;">${data.successRequests || 0}</span>
            </div>
            <div class="detail-item">
                <label>失败请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #dc3545;">${data.errorRequests || 0}</span>
            </div>
            <div class="detail-item">
                <label>成功率:</label>
                <span style="font-size: 24px; font-weight: bold; color: #28a745;">${data.successRate || '0%'}</span>
            </div>
            <div class="detail-item">
                <label>平均响应时间:</label>
                <span style="font-size: 24px; font-weight: bold; color: #667eea;">${data.avgResponseTime || 0}ms</span>
            </div>
            <div class="detail-item">
                <label>慢请求数:</label>
                <span style="font-size: 24px; font-weight: bold; color: #ffc107;">${data.slowRequests || 0}</span>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

/**
 * 加载监控数据
 */
async function loadMonitoring() {
    try {
        showLoading('monitoringContent');
        
        const response = await makeApiRequest(`${getApiBaseUrl()}/access-log/management/monitoring`);
        const result = await response.json();
        
        if (result.code === 200) {
            displayMonitoring(result.data);
        } else {
            showAlert('monitoring-alert', result.message || '加载监控数据失败');
        }
    } catch (error) {
        console.error('加载监控数据失败:', error);
        showAlert('monitoring-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示监控数据
 */
function displayMonitoring(data) {
    const container = document.getElementById('monitoringContent');
    
    const html = `
        <div style="text-align: center; padding: 40px;">
            <h3>实时监控功能</h3>
            <p style="color: #666; margin-top: 20px;">监控功能正在开发中，敬请期待...</p>
        </div>
    `;
    
    container.innerHTML = html;
}

/**
 * 启动Token状态监控
 */
function startTokenStatusMonitor() {
    // 获取Token过期时间
    const expireTimeStr = sessionStorage.getItem('accessLogTokenExpire');
    if (expireTimeStr) {
        tokenExpireTime = parseInt(expireTimeStr);
    }

    // 更新状态显示
    updateTokenStatus();

    // 启动定时器，每秒更新一次
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
    }

    tokenStatusTimer = setInterval(() => {
        updateTokenStatus();
    }, 1000);
}

/**
 * 更新Token状态显示
 */
function updateTokenStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const expireTime = document.getElementById('expireTime');

    if (!tokenExpireTime) {
        statusText.textContent = '已认证';
        expireTime.textContent = '永久有效';
        statusDot.className = 'status-dot';
        return;
    }

    const now = Date.now();
    const remainingTime = tokenExpireTime - now;

    if (remainingTime <= 0) {
        // Token已过期
        statusText.textContent = '已过期';
        expireTime.textContent = '00:00:00';
        statusDot.className = 'status-dot danger';

        // 自动跳转到登录页面
        setTimeout(() => {
            logout();
        }, 2000);
        return;
    }

    // 计算剩余时间
    const hours = Math.floor(remainingTime / (1000 * 60 * 60));
    const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    expireTime.textContent = timeStr;

    // 根据剩余时间设置状态
    if (remainingTime < 5 * 60 * 1000) { // 少于5分钟
        statusText.textContent = '即将过期';
        statusDot.className = 'status-dot danger';
    } else if (remainingTime < 15 * 60 * 1000) { // 少于15分钟
        statusText.textContent = '即将过期';
        statusDot.className = 'status-dot warning';
    } else {
        statusText.textContent = '已认证';
        statusDot.className = 'status-dot';
    }
}

/**
 * 退出登录
 */
function logout() {
    // 清除Token状态监控
    if (tokenStatusTimer) {
        clearInterval(tokenStatusTimer);
        tokenStatusTimer = null;
    }

    // 清除存储的Token信息
    sessionStorage.removeItem('accessLogToken');
    sessionStorage.removeItem('accessLogTokenExpire');

    // 重置全局变量
    accessToken = '';
    isAuthenticated = false;
    tokenExpireTime = 0;

    // 隐藏主要内容和Token状态
    document.getElementById('mainContent').style.display = 'none';
    document.getElementById('tokenStatus').style.display = 'none';

    // 显示认证区域
    document.getElementById('authSection').style.display = 'block';
    document.getElementById('secretKeyForm').style.display = 'block';
    document.getElementById('tokenForm').style.display = 'none';
    document.getElementById('validateForm').style.display = 'none';

    // 清空表单
    document.getElementById('secretKey').value = '';
    document.getElementById('code').value = '';
    document.getElementById('refreshCode').value = '';

    // 清空提示信息
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.style.display = 'none';
        alert.textContent = '';
    });

    console.log('已退出登录');
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('detailModal');
    if (event.target === modal) {
        closeDetailModal();
    }
}
