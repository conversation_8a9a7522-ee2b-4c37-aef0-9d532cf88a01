<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问日志API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>访问日志API测试页面</h1>
        <p>用于测试访问日志管理相关API接口的连通性</p>

        <!-- 测试1: 验证配置秘钥 -->
        <div class="test-section">
            <h3>测试1: 验证配置秘钥 (POST /access-log/auth/verify-secret)</h3>
            <input type="text" id="secretKey" placeholder="请输入配置秘钥" value="EDC-ACCESS-LOG-MANAGEMENT-SECRET-2025">
            <button onclick="testVerifySecret()">测试验证秘钥</button>
            <div id="secretResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试2: 管理页面访问 -->
        <div class="test-section">
            <h3>测试2: 管理页面访问 (GET /access-log/management)</h3>
            <button onclick="testManagementPage()">测试管理页面</button>
            <div id="pageResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试3: 验证Token -->
        <div class="test-section">
            <h3>测试3: 验证Token (POST /access-log/auth/verify-token)</h3>
            <input type="text" id="accessToken" placeholder="请输入访问Token">
            <button onclick="testVerifyToken()">测试验证Token</button>
            <div id="tokenResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试4: 获取数据列表 -->
        <div class="test-section">
            <h3>测试4: 获取数据列表 (GET /access-log/management/list)</h3>
            <button onclick="testDataList()">测试数据列表</button>
            <div id="listResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 获取API基础URL
        function getApiBaseUrl() {
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }

        // 显示结果
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 测试验证配置秘钥
        async function testVerifySecret() {
            const secretKey = document.getElementById('secretKey').value.trim();
            
            if (!secretKey) {
                showResult('secretResult', '请输入配置秘钥', 'error');
                return;
            }

            try {
                showResult('secretResult', '正在测试...', 'info');
                
                const response = await fetch(`${getApiBaseUrl()}/access-log/auth/verify-secret`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ secretKey })
                });

                const result = await response.text();
                
                if (response.ok) {
                    showResult('secretResult', `✅ 成功 (${response.status})\n${result}`, 'success');
                } else {
                    showResult('secretResult', `❌ 失败 (${response.status})\n${result}`, 'error');
                }
            } catch (error) {
                showResult('secretResult', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 测试管理页面访问
        async function testManagementPage() {
            try {
                showResult('pageResult', '正在测试...', 'info');
                
                const response = await fetch(`${getApiBaseUrl()}/access-log/management`);
                const result = await response.text();
                
                if (response.ok) {
                    const preview = result.length > 200 ? result.substring(0, 200) + '...' : result;
                    showResult('pageResult', `✅ 成功 (${response.status})\n页面内容预览:\n${preview}`, 'success');
                } else {
                    showResult('pageResult', `❌ 失败 (${response.status})\n${result}`, 'error');
                }
            } catch (error) {
                showResult('pageResult', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 测试验证Token
        async function testVerifyToken() {
            const accessToken = document.getElementById('accessToken').value.trim();
            
            if (!accessToken) {
                showResult('tokenResult', '请输入访问Token', 'error');
                return;
            }

            try {
                showResult('tokenResult', '正在测试...', 'info');
                
                const response = await fetch(`${getApiBaseUrl()}/access-log/auth/verify-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ accessToken })
                });

                const result = await response.text();
                
                if (response.ok) {
                    showResult('tokenResult', `✅ 成功 (${response.status})\n${result}`, 'success');
                } else {
                    showResult('tokenResult', `❌ 失败 (${response.status})\n${result}`, 'error');
                }
            } catch (error) {
                showResult('tokenResult', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 测试数据列表
        async function testDataList() {
            try {
                showResult('listResult', '正在测试...', 'info');
                
                const response = await fetch(`${getApiBaseUrl()}/access-log/management/list`);
                const result = await response.text();
                
                if (response.ok) {
                    showResult('listResult', `✅ 成功 (${response.status})\n${result}`, 'success');
                } else {
                    showResult('listResult', `❌ 失败 (${response.status})\n${result}`, 'error');
                }
            } catch (error) {
                showResult('listResult', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('访问日志API测试页面已加载');
            console.log('API基础URL:', getApiBaseUrl());
        };
    </script>
</body>
</html>
